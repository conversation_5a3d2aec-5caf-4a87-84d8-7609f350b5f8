# app/crud/stakeholder.py
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_
from fastapi import HTTPException, status

from app.models.lot import Stakeholder
from app.models.tcompany import TCompany
from app.schemas.stakeholder import StakeholderCreate, StakeholderUpdate
from app.schemas.tcompany import TCompanyCreate
from app.crud.tcompany import create_tcompany


class StakeholderCRUD:
    """
    CRUD operations pour les Stakeholders (Intervenants)
    Label français: Intervenants
    """

    def get_stakeholder(self, db: Session, stakeholder_id: int) -> Optional[Stakeholder]:
        """Récupérer un intervenant par ID"""
        return db.query(Stakeholder).filter(Stakeholder.id == stakeholder_id).first()

    def get_stakeholders_by_lot(self, db: Session, lot_id: int, skip: int = 0, limit: int = 100) -> List[Stakeholder]:
        """Récupérer tous les intervenants d'un lot"""
        return (
            db.query(Stakeholder)
            .filter(Stakeholder.lot_id == lot_id)
            .filter(Stakeholder.is_active == True)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_stakeholders_by_company(self, db: Session, company_id: int, skip: int = 0, limit: int = 100) -> List[Stakeholder]:
        """Récupérer tous les intervenants d'une entreprise"""
        return (
            db.query(Stakeholder)
            .filter(Stakeholder.company_id == company_id)
            .filter(Stakeholder.is_active == True)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_stakeholder_by_lot_and_company(self, db: Session, lot_id: int, company_id: int) -> Optional[Stakeholder]:
        """Vérifier si une entreprise est déjà intervenante sur un lot"""
        return (
            db.query(Stakeholder)
            .filter(
                and_(
                    Stakeholder.lot_id == lot_id,
                    Stakeholder.company_id == company_id,
                    Stakeholder.is_active == True
                )
            )
            .first()
        )

    def create_stakeholder(
        self, 
        db: Session, 
        stakeholder_data: StakeholderCreate, 
        current_user_id: int,
        workspace_id: int
    ) -> Stakeholder:
        """
        Créer un nouvel intervenant
        Peut soit utiliser une entreprise existante soit en créer une nouvelle
        """
        company_id = stakeholder_data.company_id

        # Si pas d'entreprise spécifiée, créer une nouvelle entreprise
        if not company_id and stakeholder_data.company_data:
            # Créer une nouvelle TCompany
            new_company = create_tcompany(
                db=db,
                tcompany_data=stakeholder_data.company_data,
                workspace_id=workspace_id
            )
            company_id = new_company.id
        elif not company_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Vous devez soit spécifier company_id soit fournir company_data"
            )

        # Vérifier que l'entreprise existe
        company = db.query(TCompany).filter(TCompany.id == company_id).first()
        if not company:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Entreprise avec l'ID {company_id} non trouvée"
            )

        # Vérifier que l'entreprise n'est pas déjà intervenante sur ce lot
        existing = self.get_stakeholder_by_lot_and_company(db, stakeholder_data.lot_id, company_id)
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"L'entreprise {company.company_name} est déjà intervenante sur ce lot"
            )

        # Créer le stakeholder
        db_stakeholder = Stakeholder(
            lot_id=stakeholder_data.lot_id,
            company_id=company_id,
            role=stakeholder_data.role,
            is_active=stakeholder_data.is_active,
            created_by=current_user_id
        )

        db.add(db_stakeholder)
        db.commit()
        db.refresh(db_stakeholder)
        return db_stakeholder

    def update_stakeholder(
        self, 
        db: Session, 
        stakeholder_id: int, 
        stakeholder_data: StakeholderUpdate
    ) -> Optional[Stakeholder]:
        """Mettre à jour un intervenant"""
        db_stakeholder = self.get_stakeholder(db, stakeholder_id)
        if not db_stakeholder:
            return None

        # Mettre à jour les champs modifiables
        update_data = stakeholder_data.dict(exclude_unset=True)
        
        # Si on change d'entreprise, vérifier qu'elle n'est pas déjà intervenante
        if "company_id" in update_data and update_data["company_id"] != db_stakeholder.company_id:
            existing = self.get_stakeholder_by_lot_and_company(
                db, db_stakeholder.lot_id, update_data["company_id"]
            )
            if existing and existing.id != stakeholder_id:
                company = db.query(TCompany).filter(TCompany.id == update_data["company_id"]).first()
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"L'entreprise {company.company_name if company else 'inconnue'} est déjà intervenante sur ce lot"
                )

        for field, value in update_data.items():
            setattr(db_stakeholder, field, value)

        db.commit()
        db.refresh(db_stakeholder)
        return db_stakeholder

    def delete_stakeholder(self, db: Session, stakeholder_id: int) -> bool:
        """Supprimer un intervenant (soft delete)"""
        db_stakeholder = self.get_stakeholder(db, stakeholder_id)
        if not db_stakeholder:
            return False

        db_stakeholder.is_active = False
        db.commit()
        return True

    def hard_delete_stakeholder(self, db: Session, stakeholder_id: int) -> bool:
        """Supprimer définitivement un intervenant"""
        db_stakeholder = self.get_stakeholder(db, stakeholder_id)
        if not db_stakeholder:
            return False

        db.delete(db_stakeholder)
        db.commit()
        return True

    def get_stakeholder_stats(self, db: Session, lot_id: Optional[int] = None) -> dict:
        """Obtenir des statistiques sur les intervenants"""
        query = db.query(Stakeholder)
        
        if lot_id:
            query = query.filter(Stakeholder.lot_id == lot_id)

        stakeholders = query.all()

        stats = {
            "total_active": sum(1 for s in stakeholders if s.is_active),
            "total_inactive": sum(1 for s in stakeholders if not s.is_active),
            "by_role": {},
            "by_lot": {}
        }

        # Statistiques par rôle
        for stakeholder in stakeholders:
            if stakeholder.is_active:
                role = stakeholder.role or "Non spécifié"
                stats["by_role"][role] = stats["by_role"].get(role, 0) + 1

        # Statistiques par lot (si pas de filtre lot_id)
        if not lot_id:
            for stakeholder in stakeholders:
                if stakeholder.is_active:
                    lot_id_key = str(stakeholder.lot_id)
                    stats["by_lot"][lot_id_key] = stats["by_lot"].get(lot_id_key, 0) + 1

        return stats

    def bulk_create_stakeholders(
        self, 
        db: Session, 
        stakeholders_data: List[StakeholderCreate], 
        current_user_id: int,
        workspace_id: int
    ) -> dict:
        """Créer plusieurs intervenants en une fois"""
        created_stakeholders = []
        errors = []

        for i, stakeholder_data in enumerate(stakeholders_data):
            try:
                stakeholder = self.create_stakeholder(
                    db=db,
                    stakeholder_data=stakeholder_data,
                    current_user_id=current_user_id,
                    workspace_id=workspace_id
                )
                created_stakeholders.append(stakeholder)
            except Exception as e:
                errors.append({
                    "index": i,
                    "data": stakeholder_data.dict(),
                    "error": str(e)
                })

        return {
            "success_count": len(created_stakeholders),
            "error_count": len(errors),
            "errors": errors if errors else None,
            "created_stakeholders": created_stakeholders
        }


# Instance globale
stakeholder_crud = StakeholderCRUD()

# Alias pour compatibilité (à supprimer après migration complète)
lot_intervenant_crud = stakeholder_crud
