#!/usr/bin/env python3
"""
Script pour créer un utilisateur admin pour tester le système d'invitation
"""

import asyncio
import asyncpg
from datetime import datetime
import uuid
from app.core.config import settings

DATABASE_URL = settings.DATABASE_URL


async def create_admin_user():
    """Créer un utilisateur admin"""
    print("👤 Création d'un utilisateur admin...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Récupérer l'entreprise ORBIS
        company = await conn.fetchrow("SELECT id, name FROM companies WHERE name = 'ORBIS'")
        if not company:
            print("   ❌ Entreprise ORBIS non trouvée")
            return False
        
        print(f"   ✅ Entreprise ORBIS trouvée (ID: {company['id']})")
        
        # Vérifier si un admin existe déjà
        existing_admin = await conn.fetchrow("""
            SELECT u.id, u.email FROM users u
            JOIN user_companies uc ON u.id = uc.user_id
            WHERE uc.company_id = $1 AND uc.role = 'admin'
            LIMIT 1
        """, company['id'])
        
        if existing_admin:
            print(f"   ✅ Utilisateur admin existe déjà: {existing_admin['email']}")
            return True
        
        # Créer un utilisateur admin
        admin_email = "<EMAIL>"
        supabase_user_id = str(uuid.uuid4())
        
        # Créer l'utilisateur
        user_id = await conn.fetchval("""
            INSERT INTO users (
                supabase_user_id, email, first_name, last_name, 
                role, is_active, is_superuser, is_verified, 
                created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING id
        """,
        supabase_user_id, admin_email, "Admin", "ORBIS", "ADMIN",
        True, True, True, datetime.utcnow(), datetime.utcnow()
        )
        
        print(f"   ✅ Utilisateur créé (ID: {user_id})")
        
        # Associer l'utilisateur à l'entreprise avec le rôle admin
        await conn.execute("""
            INSERT INTO user_companies (
                user_id, company_id, role, is_default, is_active,
                joined_at, created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        """,
        user_id, company['id'], 'admin', True, True,
        datetime.utcnow(), datetime.utcnow(), datetime.utcnow()
        )
        
        print(f"   ✅ Utilisateur associé à ORBIS avec le rôle admin")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    finally:
        await conn.close()


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Création d'un utilisateur admin")
    print("="*50)
    
    if await create_admin_user():
        print("\n✅ Utilisateur admin créé avec succès!")
        return True
    else:
        print("\n❌ Échec de la création de l'utilisateur admin")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
