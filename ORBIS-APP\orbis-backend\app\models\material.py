# app/models/material.py
from sqlalchemy import <PERSON><PERSON>an, Column, Integer, String, DateTime, ForeignKey, Text, Numeric
from sqlalchemy.orm import relationship
from datetime import datetime
from app.core.database import Base

class Material(Base):
    __tablename__ = "materials"

    id = Column(Integer, primary_key=True, index=True)
    workspace_id = Column(Integer, ForeignKey("workspaces.id"), nullable=False)
    supplier_id = Column(Integer, ForeignKey("suppliers.id"))
    category_id = Column(Integer, ForeignKey("material_categories.id"))
    name = Column(String, nullable=False, index=True)
    code = Column(String, nullable=False, index=True)
    description = Column(Text)
    unit = Column(String, nullable=False)  # m², m³, kg, etc.
    current_price = Column(Numeric(15, 4))
    minimum_quantity = Column(Numeric(10, 2))
    maximum_quantity = Column(Numeric(10, 2))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    workspace = relationship("Workspace", back_populates="materials")
    supplier = relationship("Supplier", back_populates="materials")
    category = relationship("MaterialCategory", back_populates="materials")
    technical_sheets = relationship("TechnicalSheet", back_populates="material")
    price_history = relationship("PriceHistory", back_populates="material")

class MaterialCategory(Base):
    __tablename__ = "material_categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    code = Column(String, nullable=False, index=True)
    description = Column(Text)
    parent_id = Column(Integer, ForeignKey("material_categories.id"))
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    materials = relationship("Material", back_populates="category")
    parent = relationship("MaterialCategory", remote_side=[id])

class TechnicalSheet(Base):
    __tablename__ = "technical_sheets"

    id = Column(Integer, primary_key=True, index=True)
    material_id = Column(Integer, ForeignKey("materials.id"), nullable=False)
    name = Column(String, nullable=False)
    version = Column(String)
    file_path = Column(String)
    specifications = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    material = relationship("Material", back_populates="technical_sheets")

class PriceHistory(Base):
    __tablename__ = "price_history"

    id = Column(Integer, primary_key=True, index=True)
    material_id = Column(Integer, ForeignKey("materials.id"), nullable=False)
    price = Column(Numeric(15, 4), nullable=False)
    effective_date = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    material = relationship("Material", back_populates="price_history")