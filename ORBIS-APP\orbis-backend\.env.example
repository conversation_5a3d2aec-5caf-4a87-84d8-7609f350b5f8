# Database Configuration
POSTGRES_SERVER=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=orbis_suivi_travaux
DATABASE_ECHO=false
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=11520

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379

# File Storage
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif

# CORS Origins
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:8000,https://localhost:3000,https://localhost:8000,https://orbis-frontend-83ebvd-u2nimd-99a156.mgx.dev

# Project Settings
PROJECT_NAME=ORBIS Suivi Travaux SAAS
API_V1_STR=/api/v1
