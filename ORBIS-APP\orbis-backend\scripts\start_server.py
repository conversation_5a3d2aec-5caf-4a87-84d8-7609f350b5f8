#!/usr/bin/env python3
"""
Script de démarrage du serveur ORBIS avec configuration via variables d'environnement
"""

import os
import uvicorn
from app.core.config import settings

def start_server():
    """Démarrer le serveur avec la configuration des variables d'environnement"""

    # Récupérer la configuration depuis les variables d'environnement
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", "8000"))
    frontend_port = os.getenv("FRONTEND_PORT", "3001")
    customer_port = os.getenv("CUSTOMER_PORT", "3000")

    print(f"🚀 Démarrage du serveur ORBIS...")
    print(f"   Host: {host}")
    print(f"   Port: {port}")
    print(f"   Environment: {settings.ENVIRONMENT}")
    print(f"   Debug: {settings.DEBUG}")
    print(f"   Database: {settings.DATABASE_URL.split('@')[1] if '@' in settings.DATABASE_URL else 'Local'}")
    print(f"   Supabase: {settings.SUPABASE_URL}")
    print(f"\n🌐 Configuration CORS:")
    print(f"   Frontend Admin (orbis-admin): http://localhost:{frontend_port}")
    print(f"   Frontend Client (orbis-frontend): http://localhost:{customer_port}")
    print(f"   API Backend: http://localhost:{port}")

    # Afficher les origines CORS configurées
    cors_origins = settings.BACKEND_CORS_ORIGINS
    print(f"\n🔒 Origines CORS autorisées ({len(cors_origins)}):")
    for origin in cors_origins:
        print(f"   ✅ {origin}")

    print("="*60)
    
    # Configuration uvicorn
    uvicorn_config = {
        "app": "app.main:app",
        "host": host,
        "port": port,
        "reload": settings.DEBUG,  # Auto-reload en mode debug
        "log_level": "info" if not settings.DEBUG else "debug",
    }
    
    # Démarrer le serveur
    uvicorn.run(**uvicorn_config)

if __name__ == "__main__":
    start_server()
