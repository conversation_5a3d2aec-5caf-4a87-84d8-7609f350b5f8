# app/api/api_v1/api.py
from fastapi import APIRouter

from app.api.api_v1.endpoints import (
    auth, workspaces, projects, employees, suppliers, materials,
    financial, purchase_orders, quotes, documents, technical_documents, dashboard, excel, public, invitations, admin_users, admin_workspaces, fast_auth, rbac, tcompanies, lots, stakeholders, pdf_export
)

api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(workspaces.router, prefix="/workspaces", tags=["workspaces"])

api_router.include_router(projects.router, prefix="/projects", tags=["projects"])
api_router.include_router(employees.router, prefix="/employees", tags=["employees"])
api_router.include_router(suppliers.router, prefix="/suppliers", tags=["suppliers"])
api_router.include_router(materials.router, prefix="/materials", tags=["materials"])
api_router.include_router(financial.router, prefix="/financial", tags=["financial"])
api_router.include_router(purchase_orders.router, prefix="/purchase-orders", tags=["purchase-orders"])
api_router.include_router(quotes.router, prefix="/quotes", tags=["quotes"])
api_router.include_router(documents.router, prefix="/documents", tags=["documents"])
api_router.include_router(technical_documents.router, prefix="/technical-documents", tags=["technical-documents"])
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["dashboard"])
api_router.include_router(excel.router, prefix="/excel", tags=["excel"])
api_router.include_router(public.router, prefix="/public", tags=["public"])
api_router.include_router(invitations.router, prefix="/invitations", tags=["invitations"])
api_router.include_router(admin_users.router, prefix="/admin/users", tags=["admin-users"])
api_router.include_router(admin_workspaces.router, prefix="/admin/workspaces", tags=["admin-workspaces"])

api_router.include_router(rbac.router, prefix="/admin/rbac", tags=["rbac"])
api_router.include_router(fast_auth.router, prefix="/fast-auth", tags=["fast-authentication"])
api_router.include_router(tcompanies.router, prefix="/tcompanies", tags=["tcompanies"])
api_router.include_router(lots.router, prefix="/lots", tags=["lots"])
api_router.include_router(stakeholders.router, prefix="/stakeholders", tags=["stakeholders"])
api_router.include_router(pdf_export.router, prefix="/pdf", tags=["pdf-export"])
