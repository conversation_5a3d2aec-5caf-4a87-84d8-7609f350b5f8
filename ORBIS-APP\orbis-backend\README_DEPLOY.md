# 🚀 Déploiement ORBIS Backend sur Render.com

Ce guide vous explique comment déployer ORBIS Backend sur Render.com en quelques minutes.

## 📋 Prérequis

- Compte Render.com gratuit
- Git installé
- Code source ORBIS Backend

## 🎯 Méthode 1: Déploiement Automatisé (Recommandé)

### 1. <PERSON><PERSON><PERSON> le projet
```bash
# Sur Windows avec PowerShell
.\clean_for_production.ps1

# Sur Linux/Mac
python -c "import os; [os.remove(f) for f in ['test_lots_results.json', 'test_user_credentials.json', 'auth_token.txt', 'weasyprint_styles_debug.css', 'template_config.json'] if os.path.exists(f)]"
```

### 2. Créer le repository Git
```bash
git init
git add .
git commit -m "Initial commit - ORBIS Backend ready for Render"
git remote add origin https://github.com/YOUR_USERNAME/orbis-backend.git
git push -u origin main
```

### 3. Déployer sur Render.com

**Option A: Déploiement via GitHub**
1. Allez sur [render.com](https://render.com)
2. Cliquez sur "New" → "Web Service"
3. Connectez votre repository GitHub
4. Sélectionnez le repository `orbis-backend`
5. Configurez avec les paramètres ci-dessous

**Option B: Déploiement via fichier render.yaml**
1. Allez sur [render.com](https://render.com)
2. Cliquez sur "New" → "Blueprint
3. Collez l'URL de votre repository GitHub
4. Render détectera automatiquement le fichier `render.yaml`

## ⚙️ Configuration Render

### Variables d'environnement requises

| Variable | Description | Exemple |
|----------|-------------|---------|
| `ENVIRONMENT` | Mode production | `production` |
| `DEBUG` | Mode debug | `false` |
| `DATABASE_URL` | URL PostgreSQL (auto-générée) | `postgresql://...` |
| `SECRET_KEY` | Clé secrète (auto-générée) | `your-secret-key` |
| `SUPABASE_URL` | URL Supabase (optionnel) | `https://...supabase.co` |
| `SUPABASE_ANON_KEY` | Clé publique Supabase | `eyJ...` |
| `SUPABASE_SERVICE_ROLE_KEY` | Clé service Supabase | `eyJ...` |
| `OPENAI_API_KEY` | Clé OpenAI (optionnel) | `sk-...` |

### Configuration minimale
```yaml
# render.yaml (déjà inclus)
```

## 🐳 Configuration Docker Alternative

Si vous préférez utiliser Docker directement :

### Dockerfile.render
```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## 🚀 Commandes de déploiement rapide

### Méthode 1: One-click deploy
[![Deploy to Render](https://render.com/images/deploy-to-render-button.svg)](https://render.com/deploy?repo=https://github.com/YOUR_USERNAME/orbis-backend)

### Méthode 2: CLI Render
```bash
# Installer Render CLI
npm install -g @render/cli

# Déployer
render deploy
```

## 📊 Monitoring

### Logs
- Accédez aux logs via le dashboard Render
- Utilisez `render logs` via CLI

### Health checks
L'endpoint `/health` est disponible pour les health checks.

## 🔧 Configuration post-déploiement

### 1. Créer l'administrateur
```bash
# Via Render CLI
render run orbis-backend python scripts/create_admin_user.py

# Ou via le dashboard
render shell orbis-backend
python scripts/create_admin_user.py
```

### 2. Seeder la base de données
```bash
render run orbis-backend python scripts/seed_db.py
```

### 3. Tester l'API
```bash
curl https://YOUR_APP.onrender.com/health
```

## 🎛️ Configuration avancée

### Plan de base de données
- **Starter**: Gratuit, 1GB RAM, 1GB storage
- **Standard**: $7/mois, 2GB RAM, 10GB storage
- **Pro**: $25/mois, 4GB RAM, 64GB storage

### Variables d'environnement secrètes
Dans le dashboard Render → Settings → Environment Variables

### Custom domains
Ajoutez votre domaine personnalisé dans Settings → Custom Domains

## 🚨 Dépannage

### Erreurs courantes

**Erreur: Module not found**
```bash
# Vérifier requirements.txt
pip install -r requirements.txt
```

**Erreur: Database connection**
```bash
# Vérifier DATABASE_URL
echo $DATABASE_URL
```

**Erreur: Port already in use**
```bash
# Utiliser le port Render
uvicorn app.main:app --host 0.0.0.0 --port $PORT
```

### Support
- Documentation Render: [docs.render.com](https://docs.render.com)
- Support Render: <EMAIL>
- Issues GitHub: [orbis-backend/issues](https://github.com/YOUR_USERNAME/orbis-backend/issues)

## 🎉 Succès !

Votre application ORBIS Backend est maintenant déployée sur Render.com !

**URLs:**
- API: `https://YOUR_APP.onrender.com`
- Documentation: `https://YOUR_APP.onrender.com/docs`
- Health check: `https://YOUR_APP.onrender.com/health`
