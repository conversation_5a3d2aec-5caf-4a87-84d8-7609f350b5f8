# app/api/api_v1/endpoints/projects.py
"""
CRUD complet pour les projets avec authentification JWT
Version avec table project_company
"""

from typing import Any, List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, File, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload
from pathlib import Path
import uuid

from app.api import deps
from app.models.project import Project, ProjectStatus, ProjectNature, ProjectCompany
from app.models.user import User
from app.models.workspace import Workspace, UserWorkspace
from app.models.tcompany import TCompany
from app.schemas.project import ProjectResponse, ProjectCreate, ProjectUpdate
from app.middleware.auth_sync_middleware import require_auth

router = APIRouter()

@router.get("/", response_model=List[ProjectResponse])
async def get_projects(
    db: AsyncSession = Depends(deps.get_db),
    skip: int = Query(0, ge=0, description="Nombre d'éléments à ignorer"),
    limit: int = Query(100, ge=1, le=1000, description="Nombre maximum d'éléments à retourner"),
    status: Optional[ProjectStatus] = Query(None, description="Filtrer par statut"),
    nature: Optional[ProjectNature] = Query(None, description="Filtrer par nature"),
    search: Optional[str] = Query(None, description="Recherche dans le nom, code, description ou client"),
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Récupérer la liste des projets avec filtres et pagination
    """
    try:
        print(f"🔍 GET /projects - User: {current_user}")

        user_id = current_user.get("id") or current_user.get("user_id")
        user_role = current_user.get("role", "").upper()
        is_superuser = current_user.get("is_superuser", False)

        print(f"🔍 User ID: {user_id}, Role: {user_role}, Superuser: {is_superuser}")

        # Construire la requête de base
        query = select(Project)
        print(f"🔍 Base query created")

        # Filtrage par entreprise (sauf pour les super admins)
        if not is_superuser:
            print(f"🔍 Non-superuser, filtering by workspace...")

            # Récupérer l'espace de travail de l'utilisateur via UserWorkspace
            user_workspace_result = await db.execute(
                select(UserWorkspace).where(
                    and_(
                        UserWorkspace.user_id == user_id,
                        UserWorkspace.is_active == True
                    )
                ).limit(1)
            )
            user_workspace = user_workspace_result.scalar_one_or_none()
            print(f"🔍 User workspace query result: {user_workspace}")

            if not user_workspace:
                raise HTTPException(
                    status_code=403,
                    detail=f"Utilisateur {user_id} non associé à un espace de travail actif"
                )

            print(f"🔍 User workspace ID: {user_workspace.workspace_id}")

            # Filtrer les projets par workspace_id directement
            query = query.where(Project.workspace_id == user_workspace.workspace_id)
            print(f"🔍 Project filtering query created for workspace {user_workspace.workspace_id}")
        else:
            print(f"🔍 Superuser - no company filtering")

        # Filtres optionnels
        if status:
            query = query.where(Project.status == status)
            print(f"🔍 Added status filter: {status}")

        if nature:
            query = query.where(Project.nature == nature)
            print(f"🔍 Added nature filter: {nature}")

        # Recherche textuelle
        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    Project.name.ilike(search_term),
                    Project.code.ilike(search_term),
                    Project.description.ilike(search_term),
                    Project.client_name.ilike(search_term)
                )
            )
            print(f"🔍 Added search filter: {search}")

        # Exclure les projets archivés par défaut
        query = query.where(Project.is_archived == False)
        print(f"🔍 Added archived filter")

        # Tri par date de création (plus récent en premier)
        query = query.order_by(Project.created_at.desc())
        print(f"🔍 Added ordering")

        # Pagination
        query = query.offset(skip).limit(limit)
        print(f"🔍 Added pagination: skip={skip}, limit={limit}")

        # Exécuter la requête
        print(f"🔍 Executing query...")
        result = await db.execute(query)
        projects = result.scalars().all()
        print(f"✅ Query executed successfully, found {len(projects)} projects")

        return projects

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Unexpected error in get_projects: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=500,
            detail=f"Erreur inattendue: {str(e)}"
        )

@router.post("/", response_model=ProjectResponse)
async def create_project(
    *,
    db: AsyncSession = Depends(deps.get_db),
    project_in: ProjectCreate,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Créer un nouveau projet
    """
    user_id = current_user.get("id") or current_user.get("user_id")
    if not user_id:
        raise HTTPException(status_code=400, detail="Token utilisateur invalide")

    # Récupérer l'espace de travail de l'utilisateur via UserWorkspace
    user_workspace_result = await db.execute(
        select(UserWorkspace).where(
            and_(
                UserWorkspace.user_id == user_id,
                UserWorkspace.is_active == True
            )
        ).limit(1)
    )
    user_workspace = user_workspace_result.scalar_one_or_none()
    if not user_workspace:
        raise HTTPException(status_code=403, detail="Utilisateur non associé à un espace de travail")

    # Générer un code unique pour le projet si non fourni
    if not project_in.code:
        # Compter les projets existants pour ce workspace
        count_result = await db.execute(
            select(func.count(Project.id)).where(Project.workspace_id == user_workspace.workspace_id)
        )
        project_count = count_result.scalar() or 0
        project_in.code = f"PRJ-{user_workspace.workspace_id:03d}-{project_count + 1:04d}"

    # Vérifier que le code n'existe pas déjà
    existing_result = await db.execute(
        select(Project).where(Project.code == project_in.code)
    )
    if existing_result.scalar_one_or_none():
        raise HTTPException(status_code=400, detail="Un projet avec ce code existe déjà")

    # Créer le projet
    project_data = project_in.dict(exclude_unset=True)
    project_data['workspace_id'] = user_workspace.workspace_id

    # Extraire tcompany_id pour traitement séparé
    tcompany_id = project_data.pop('tcompany_id', None)

    db_project = Project(**project_data)
    db.add(db_project)
    await db.flush()  # Pour obtenir l'ID du projet

    # Créer une relation avec une TCompany si spécifiée
    if tcompany_id:
        # TODO: Créer la relation ProjectCompany avec la TCompany
        # Cette partie sera implémentée plus tard selon les besoins métier
        pass

    await db.commit()
    await db.refresh(db_project)

    return db_project

@router.get("/{project_id}", response_model=ProjectResponse)
async def get_project(
    *,
    db: AsyncSession = Depends(deps.get_db),
    project_id: int,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Récupérer un projet par son ID
    """
    user_id = current_user.get("id") or current_user.get("user_id")
    is_superuser = current_user.get("is_superuser", False)
    
    # Construire la requête avec les relations
    query = select(Project).options(
        selectinload(Project.companies).selectinload(ProjectCompany.company)
    ).where(Project.id == project_id)

    # Filtrage par entreprise (sauf pour les super admins)
    if not is_superuser:
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        if not user_workspace:
            raise HTTPException(status_code=403, detail="Accès non autorisé")

        # Filtrer par workspace_id directement


        query = query.where(Project.workspace_id == user_workspace.workspace_id)
    
    result = await db.execute(query)
    project = result.scalar_one_or_none()
    
    if not project:
        raise HTTPException(status_code=404, detail="Projet non trouvé")
    
    return project

@router.put("/{project_id}", response_model=ProjectResponse)
async def update_project(
    *,
    db: AsyncSession = Depends(deps.get_db),
    project_id: int,
    project_in: ProjectUpdate,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Mettre à jour un projet
    """
    user_id = current_user.get("id") or current_user.get("user_id")
    is_superuser = current_user.get("is_superuser", False)

    # Récupérer le projet existant
    query = select(Project).where(Project.id == project_id)
    
    # Filtrage par entreprise (sauf pour les super admins)
    if not is_superuser:
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        if not user_workspace:
            raise HTTPException(status_code=403, detail="Accès non autorisé")

        # Filtrer par workspace_id directement


        query = query.where(Project.workspace_id == user_workspace.workspace_id)
    
    result = await db.execute(query)
    db_project = result.scalar_one_or_none()
    
    if not db_project:
        raise HTTPException(status_code=404, detail="Projet non trouvé")
    
    # Mettre à jour les champs fournis
    update_data = project_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_project, field, value)
    
    await db.commit()
    await db.refresh(db_project)
    
    return db_project

@router.delete("/{project_id}")
async def delete_project(
    *,
    db: AsyncSession = Depends(deps.get_db),
    project_id: int,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Supprimer un projet (soft delete - archivage)
    """
    user_id = current_user.get("id") or current_user.get("user_id")
    is_superuser = current_user.get("is_superuser", False)

    # Récupérer le projet existant
    query = select(Project).where(Project.id == project_id)
    
    # Filtrage par entreprise (sauf pour les super admins)
    if not is_superuser:
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        if not user_workspace:
            raise HTTPException(status_code=403, detail="Accès non autorisé")

        # Filtrer par workspace_id directement


        query = query.where(Project.workspace_id == user_workspace.workspace_id)
    
    result = await db.execute(query)
    db_project = result.scalar_one_or_none()
    
    if not db_project:
        raise HTTPException(status_code=404, detail="Projet non trouvé")
    
    # Archiver le projet au lieu de le supprimer
    db_project.is_archived = True
    await db.commit()
    
    return {"message": "Projet archivé avec succès"}

@router.post("/{project_id}/restore")
async def restore_project(
    *,
    db: AsyncSession = Depends(deps.get_db),
    project_id: int,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Restaurer un projet archivé
    """
    user_id = current_user.get("id") or current_user.get("user_id")
    is_superuser = current_user.get("is_superuser", False)

    # Récupérer le projet existant (même archivé)
    query = select(Project).where(Project.id == project_id)
    
    # Filtrage par entreprise (sauf pour les super admins)
    if not is_superuser:
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        if not user_workspace:
            raise HTTPException(status_code=403, detail="Accès non autorisé")

        # Filtrer par workspace_id directement


        query = query.where(Project.workspace_id == user_workspace.workspace_id)
    
    result = await db.execute(query)
    db_project = result.scalar_one_or_none()
    
    if not db_project:
        raise HTTPException(status_code=404, detail="Projet non trouvé")
    
    # Restaurer le projet
    db_project.is_archived = False
    await db.commit()
    
    return {"message": "Projet restauré avec succès"}

@router.get("/stats/summary")
async def get_projects_stats(
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Obtenir les statistiques des projets
    """
    user_id = current_user.get("id") or current_user.get("user_id")
    is_superuser = current_user.get("is_superuser", False)

    # Base query
    base_query = select(Project)
    
    # Filtrage par entreprise (sauf pour les super admins)
    if not is_superuser:
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        if not user_workspace:
            raise HTTPException(status_code=403, detail="Accès non autorisé")

        # Filtrer par workspace_id directement


        base_query = base_query.where(Project.workspace_id == user_workspace.workspace_id)
    
    # Statistiques par statut
    stats_by_status = {}
    for status in ProjectStatus:
        count_result = await db.execute(
            select(func.count(Project.id)).where(
                and_(
                    base_query.whereclause,
                    Project.status == status,
                    Project.is_archived == False
                )
            )
        )
        stats_by_status[status.value] = count_result.scalar() or 0
    
    # Statistiques par nature (nombre et budget)
    stats_by_nature = {}
    budgets_by_nature = {}
    for nature_enum in ProjectNature:
        # Compter les projets
        count_result = await db.execute(
            select(func.count(Project.id)).where(
                and_(
                    base_query.whereclause,
                    Project.nature == nature_enum,
                    Project.is_archived == False
                )
            )
        )
        stats_by_nature[nature_enum.value] = count_result.scalar() or 0

        # Calculer le budget total
        budget_result = await db.execute(
            select(func.coalesce(func.sum(Project.budget_total), 0)).where(
                and_(
                    base_query.whereclause,
                    Project.nature == nature_enum,
                    Project.is_archived == False
                )
            )
        )
        budgets_by_nature[nature_enum.value] = float(budget_result.scalar() or 0)
    
    # Total des projets actifs
    total_result = await db.execute(
        select(func.count(Project.id)).where(
            and_(
                base_query.whereclause,
                Project.is_archived == False
            )
        )
    )
    total_active = total_result.scalar() or 0
    
    # Total des projets archivés
    archived_result = await db.execute(
        select(func.count(Project.id)).where(
            and_(
                base_query.whereclause,
                Project.is_archived == True
            )
        )
    )
    total_archived = archived_result.scalar() or 0

    # Budget total de tous les projets actifs
    total_budget_result = await db.execute(
        select(func.coalesce(func.sum(Project.budget_total), 0)).where(
            and_(
                base_query.whereclause,
                Project.is_archived == False
            )
        )
    )
    total_budget = float(total_budget_result.scalar() or 0)

    return {
        "total_active": total_active,
        "total_archived": total_archived,
        "total_budget": total_budget,
        "by_status": stats_by_status,
        "by_nature": stats_by_nature,
        "budgets_by_nature": budgets_by_nature
    }


@router.post("/{project_id}/upload-photo")
async def upload_project_photo(
    *,
    project_id: int,
    file: UploadFile = File(...),
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Upload de la photo d'un projet"""
    try:
        print(f"📤 Upload photo pour Project {project_id}")

        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Vérifier le type de fichier
        allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"]
        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=400,
                detail=f"Type de fichier non autorisé. Types acceptés: {', '.join(allowed_types)}"
            )

        # Vérifier la taille du fichier (max 10MB)
        file_content = await file.read()
        if len(file_content) > 10 * 1024 * 1024:  # 10MB
            raise HTTPException(status_code=400, detail="Fichier trop volumineux (max 10MB)")

        # Récupérer l'espace de travail de l'utilisateur
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()

        if not user_workspace:
            raise HTTPException(status_code=404, detail="Aucun espace de travail trouvé pour cet utilisateur")

        workspace_id = user_workspace.workspace_id

        # Récupérer le projet
        result = await db.execute(
            select(Project).where(
                and_(
                    Project.id == project_id,
                    Project.workspace_id == workspace_id
                )
            )
        )
        db_project = result.scalar_one_or_none()

        if not db_project:
            raise HTTPException(status_code=404, detail="Projet non trouvé")

        # Créer le dossier de stockage s'il n'existe pas
        upload_dir = Path("uploads/project_photos")
        upload_dir.mkdir(parents=True, exist_ok=True)

        # Générer un nom de fichier unique
        file_extension = Path(file.filename).suffix.lower()
        unique_filename = f"project_{project_id}_{uuid.uuid4().hex}{file_extension}"
        file_path = upload_dir / unique_filename

        # Supprimer l'ancienne photo s'il existe
        if db_project.photo_filename:
            old_file_path = upload_dir / db_project.photo_filename
            if old_file_path.exists():
                old_file_path.unlink()
                print(f"🗑️ Ancienne photo supprimée: {old_file_path}")

        # Sauvegarder le nouveau fichier
        with open(file_path, "wb") as buffer:
            buffer.write(file_content)

        # Mettre à jour la base de données
        db_project.photo_filename = unique_filename
        db_project.photo_url = f"/uploads/project_photos/{unique_filename}"

        await db.commit()
        await db.refresh(db_project)

        print(f"✅ Photo uploadée avec succès: {unique_filename}")

        return {
            "message": "Photo uploadée avec succès",
            "photo_filename": unique_filename,
            "photo_url": db_project.photo_url
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Erreur upload photo Project {project_id}: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Erreur lors de l'upload de la photo: {str(e)}")


@router.delete("/{project_id}/photo")
async def delete_project_photo(
    *,
    project_id: int,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Supprimer la photo d'un projet"""
    try:
        print(f"🗑️ Suppression photo Project {project_id}")

        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()

        if not user_workspace:
            raise HTTPException(status_code=404, detail="Aucun espace de travail trouvé pour cet utilisateur")

        workspace_id = user_workspace.workspace_id

        # Récupérer le projet
        result = await db.execute(
            select(Project).where(
                and_(
                    Project.id == project_id,
                    Project.workspace_id == workspace_id
                )
            )
        )
        db_project = result.scalar_one_or_none()

        if not db_project:
            raise HTTPException(status_code=404, detail="Projet non trouvé")

        if not db_project.photo_filename:
            raise HTTPException(status_code=404, detail="Aucune photo à supprimer")

        # Supprimer le fichier physique
        upload_dir = Path("uploads/project_photos")
        file_path = upload_dir / db_project.photo_filename
        if file_path.exists():
            file_path.unlink()
            print(f"🗑️ Fichier photo supprimé: {file_path}")

        # Mettre à jour la base de données
        db_project.photo_filename = None
        db_project.photo_url = None

        await db.commit()

        print(f"✅ Photo Project {project_id} supprimée avec succès")

        return {"message": "Photo supprimée avec succès"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Erreur suppression photo Project {project_id}: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Erreur lors de la suppression de la photo: {str(e)}")
