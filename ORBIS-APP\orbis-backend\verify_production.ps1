# Vérification ORBIS Backend après Nettoyage - PowerShell Script
# Ce script vérifie que l'application fonctionne correctement après le nettoyage

Write-Host "🔍 Vérification ORBIS Backend après Nettoyage" -ForegroundColor Blue
Write-Host "===========================================" -ForegroundColor Blue

# 1. Vérifier la structure de base
Write-Host "`n📁 Vérification de la structure..." -ForegroundColor Yellow

$requiredFolders = @("app", "alembic", "scripts", "tests", "uploads")
$missingFolders = @()

foreach ($folder in $requiredFolders) {
    if (Test-Path $folder) {
        Write-Host "✅ $folder" -ForegroundColor Green
    } else {
        Write-Host "❌ $folder - MANQUANT" -ForegroundColor Red
        $missingFolders += $folder
    }
}

# 2. Vérifier les fichiers essentiels
Write-Host "`n📄 Vérification des fichiers essentiels..." -ForegroundColor Yellow

$requiredFiles = @(
    "app/main.py",
    "app/core/config.py",
    "requirements.txt",
    "Dockerfile",
    "docker-compose.yml",
    ".env.example"
)

$missingFiles = @()
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file - MANQUANT" -ForegroundColor Red
        $missingFiles += $file
    }
}

# 3. Vérifier les scripts de production
Write-Host "`n🐍 Vérification des scripts de production..." -ForegroundColor Yellow

$productionScripts = @(
    "scripts/create_admin_user.py",
    "scripts/create_orbis_user.py",
    "scripts/seed_db.py",
    "scripts/start_server.py",
    "scripts/run_migration.py"
)

$missingScripts = @()
foreach ($script in $productionScripts) {
    if (Test-Path $script) {
        Write-Host "✅ $script" -ForegroundColor Green
    } else {
        Write-Host "❌ $script - MANQUANT" -ForegroundColor Red
        $missingScripts += $script
    }
}

# 4. Vérifier qu'il n'y a plus de fichiers de test
Write-Host "`n🧪 Vérification des fichiers de test restants..." -ForegroundColor Yellow

$testFiles = @(
    "test_lots_results.json",
    "test_user_credentials.json",
    "auth_token.txt",
    "weasyprint_styles_debug.css",
    "template_config.json"
)

$remainingTestFiles = @()
foreach ($file in $testFiles) {
    if (Test-Path $file) {
        Write-Host "⚠️  $file - ENCORE PRÉSENT" -ForegroundColor Yellow
        $remainingTestFiles += $file
    }
}

# 5. Vérifier le dossier scripts pour les fichiers de test
Write-Host "`n🔍 Vérification des scripts de test restants..." -ForegroundColor Yellow

$testScripts = Get-ChildItem -Path "scripts" -Filter "test_*.py" -File -ErrorAction SilentlyContinue
if ($testScripts.Count -gt 0) {
    Write-Host "⚠️  Scripts de test trouvés:" -ForegroundColor Yellow
    foreach ($script in $testScripts) {
        Write-Host "   📄 $($script.Name)" -ForegroundColor Yellow
    }
} else {
    Write-Host "✅ Aucun script de test trouvé" -ForegroundColor Green
}

# 6. Résumé
Write-Host "`n📊 Résumé de la vérification:" -ForegroundColor Cyan

$issues = $missingFolders.Count + $missingFiles.Count + $missingScripts.Count + $remainingTestFiles.Count + $testScripts.Count

if ($issues -eq 0) {
    Write-Host "🎉 TOUT EST OK ! Le projet est prêt pour la production." -ForegroundColor Green
    Write-Host "`n💡 Pour démarrer l'application:" -ForegroundColor Cyan
    Write-Host "   docker-compose up --build" -ForegroundColor White
    Write-Host "   OU" -ForegroundColor Gray
    Write-Host "   python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload" -ForegroundColor White
} else {
    Write-Host "⚠️  $issues problème(s) détecté(s)" -ForegroundColor Red
    
    if ($missingFolders.Count -gt 0) {
        Write-Host "   ❌ Dossiers manquants: $($missingFolders -join ', ')" -ForegroundColor Red
    }
    
    if ($missingFiles.Count -gt 0) {
        Write-Host "   ❌ Fichiers manquants: $($missingFiles -join ', ')" -ForegroundColor Red
    }
    
    if ($missingScripts.Count -gt 0) {
        Write-Host "   ❌ Scripts manquants: $($missingScripts -join ', ')" -ForegroundColor Red
    }
    
    if ($remainingTestFiles.Count -gt 0) {
        Write-Host "   ⚠️  Fichiers de test à supprimer: $($remainingTestFiles -join ', ')" -ForegroundColor Yellow
    }
    
    if ($testScripts.Count -gt 0) {
        Write-Host "   ⚠️  Scripts de test à supprimer: $($testScripts.Name -join ', ')" -ForegroundColor Yellow
    }
}

# 7. Test de démarrage rapide (optionnel)
Write-Host "`n🚀 Test de démarrage rapide..." -ForegroundColor Blue
if (Test-Path "requirements.txt") {
    Write-Host "✅ requirements.txt présent - pip install -r requirements.txt" -ForegroundColor Green
}
if (Test-Path "Dockerfile") {
    Write-Host "✅ Dockerfile présent - docker build possible" -ForegroundColor Green
}
if (Test-Path "docker-compose.yml") {
    Write-Host "✅ docker-compose.yml présent - docker-compose up possible" -ForegroundColor Green
}

Write-Host "`n✅ Vérification terminée !" -ForegroundColor Green
