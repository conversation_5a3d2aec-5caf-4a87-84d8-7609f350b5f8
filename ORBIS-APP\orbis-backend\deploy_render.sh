#!/bin/bash

# Script de déploiement ORBIS Backend pour Render.com
# Compatible Linux/MacOS

set -e

echo "==============================================="
echo "    🚀 ORBIS Backend - Déploiement Render.com"
echo "==============================================="
echo

# Couleurs
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}📋 Vérification de la configuration...${NC}"
echo

# Vérifier les fichiers nécessaires
if [ -f "render.yaml" ]; then
    echo -e "${GREEN}✅ render.yaml trouvé${NC}"
else
    echo -e "${RED}❌ render.yaml manquant${NC}"
    exit 1
fi

if [ -f "Dockerfile.render" ]; then
    echo -e "${GREEN}✅ Dockerfile.render trouvé${NC}"
else
    echo -e "${RED}❌ Dockerfile.render manquant${NC}"
    exit 1
fi

echo
echo -e "${YELLOW}🧹 Nettoyage du projet...${NC}"
echo

# Nettoyer les fichiers de test
rm -f test_lots_results.json test_user_credentials.json auth_token.txt weasyprint_styles_debug.css template_config.json

# Nettoyer les scripts de test
find scripts -name "test_*.py" -delete 2>/dev/null || true
find scripts -name "check_*.py" -delete 2>/dev/null || true
find scripts -name "debug_*.py" -delete 2>/dev/null || true

echo -e "${GREEN}✅ Nettoyage terminé${NC}"

echo
echo -e "${YELLOW}📝 Instructions de déploiement :${NC}"
echo
echo "1. Poussez votre code sur GitHub"
echo "2. Allez sur https://render.com"
echo "3. Cliquez sur 'New' -> 'Web Service'"
echo "4. Connectez votre repository GitHub"
echo "5. Sélectionnez le repository orbis-backend"
echo
echo -e "${YELLOW}🎯 Configuration rapide :${NC}"
echo "   Build Command: pip install -r requirements.txt"
echo "   Start Command: uvicorn app.main:app --host 0.0.0.0 --port \$PORT"
echo "   Environment: Python 3.11"
echo
echo -e "${YELLOW}🌐 Deploy to Render button :${NC}"
echo "   https://render.com/deploy?repo=https://github.com/YOUR_USERNAME/orbis-backend"
echo
echo "==============================================="
echo -e "${GREEN}    ✅ Projet prêt pour le déploiement !${NC}"
echo "==============================================="

# Optionnel: créer un repository git si ce n'est pas déjà fait
if [ ! -d ".git" ]; then
    echo
    echo -e "${YELLOW}📝 Initialisation du repository Git...${NC}"
    git init
    git add .
    git commit -m "Initial commit - ORBIS Backend ready for Render"
    echo -e "${GREEN}✅ Repository Git initialisé${NC}"
fi
