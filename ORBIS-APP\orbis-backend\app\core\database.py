# app/core/database.py
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import declarative_base, sessionmaker
from app.core.config import settings

# Create async engine with PostgreSQL for Supabase
# Use a more aggressive approach to disable prepared statements
engine = create_async_engine(
    settings.ASYNC_DATABASE_URL,
    echo=settings.DATABASE_ECHO,
    pool_pre_ping=True,
    pool_recycle=60,    # Very frequent recycling to avoid cached statements
    pool_size=1,        # Single connection to avoid conflicts
    max_overflow=0,     # No overflow connections
    # Completely disable prepared statements for pgbouncer
    connect_args={
        "statement_cache_size": 0,
        "prepared_statement_cache_size": 0,
        "server_settings": {
            "application_name": "orbis_api",
        }
    },
    # Additional engine options to disable statement caching
    execution_options={
        "compiled_cache": {},
        "autocommit": False,
    }
)

# Async session factory
AsyncSessionLocal = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)

Base = declarative_base()

# Dependency to get async database session
async def get_db() -> AsyncSession:
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()

# Initialize database
async def init_db():
    """Create database tables"""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

# Close database connections
async def close_db():
    """Close database connections"""
    await engine.dispose()
