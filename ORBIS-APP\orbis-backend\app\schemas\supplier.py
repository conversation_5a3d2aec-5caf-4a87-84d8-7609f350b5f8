# app/schemas/supplier.py
from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from decimal import Decimal

class SupplierBase(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    type: Optional[str] = None
    address: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    website: Optional[str] = None
    siret: Optional[str] = None
    vat_number: Optional[str] = None
    payment_terms: Optional[str] = None
    is_active: Optional[bool] = True
    rating: Optional[Decimal] = None

class SupplierCreate(SupplierBase):
    name: str
    code: str
    workspace_id: int

class SupplierUpdate(SupplierBase):
    pass

class SupplierInDBBase(SupplierBase):
    id: Optional[int] = None
    workspace_id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Supplier(SupplierInDBBase):
    pass

class SupplierContact(BaseModel):
    id: Optional[int] = None
    supplier_id: int
    first_name: str
    last_name: str
    position: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    is_primary: Optional[bool] = False

    class Config:
        from_attributes = True