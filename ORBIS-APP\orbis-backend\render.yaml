# Configuration Render.com pour ORBIS Backend
services:
  # Service principal - API FastAPI
  - type: web
    name: orbis-backend
    env: python
    buildCommand: pip install -r requirements.txt
    startCommand: uvicorn app.main:app --host 0.0.0.0 --port $PORT
    envVars:
      # Variables d'environnement de base
      - key: ENVIRONMENT
        value: production
      - key: DEBUG
        value: false
      
      # Database - PostgreSQL via Render
      - key: DATABASE_URL
        fromDatabase:
          name: orbis-postgres
          property: connectionString
      
      # Supabase (si utilisé)
      - key: SUPABASE_URL
        sync: false
      - key: SUPABASE_ANON_KEY
        sync: false
      - key: SUPABASE_SERVICE_ROLE_KEY
        sync: false
      
      # OpenAI (optionnel)
      - key: OPENAI_API_KEY
        sync: false
      
      # Security
      - key: SECRET_KEY
        generateValue: true
      
      # CORS
      - key: BACKEND_CORS_ORIGINS
        value: '["*"]'

# Base de données PostgreSQL
databases:
  - name: orbis-postgres
    databaseName: orbis_suivi_travaux
    user: orbis_user
    plan: starter  # starter, standard, pro

# Configuration des fichiers statiques (optionnel)
# staticPublishPath: ./static
