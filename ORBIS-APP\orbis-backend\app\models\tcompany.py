"""
Modèle TCompany - Version améliorée et standardisée d'EntrepriseTiers
Standardisation des noms de colonnes en anglais avec validation renforcée
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey, Text, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base


class TCompany(Base):
    """
    Modèle pour les entreprises tierces (Third-party Companies)
    Version standardisée avec noms de colonnes en anglais
    """
    __tablename__ = "tcompanies"

    id = Column(Integer, primary_key=True, index=True)
    
    # Informations de base - STANDARDISÉES EN ANGLAIS
    company_name = Column(String(255), nullable=False, index=True)
    activity = Column(String(255), nullable=True)
    
    # Adresse - STANDARDISÉE
    address = Column(Text, nullable=True)
    postal_code = Column(String(10), nullable=True)
    city = Column(String(100), nullable=True)
    country = Column(String(100), nullable=True, default="France")
    
    # Contact - STANDARDISÉ
    phone = Column(String(20), nullable=True)
    fax = Column(String(20), nullable=True)
    email = Column(String(320), nullable=True, index=True)  # RFC 5321 max length
    
    # Informations légales - STANDARDISÉES
    siret = Column(String(14), nullable=True, index=True)
    vat_number = Column(String(20), nullable=True)  # ex: tva_intracommunautaire
    
    # Représentant légal - STANDARDISÉ
    legal_representative_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Logo de l'entreprise - STANDARDISÉ
    logo_url = Column(String(500), nullable=True)
    logo_filename = Column(String(255), nullable=True)

    # Relation avec l'espace de travail propriétaire
    workspace_id = Column(Integer, ForeignKey("workspaces.id"), nullable=False, index=True)

    # Métadonnées - STANDARDISÉES
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relations - STANDARDISÉES
    workspace = relationship("Workspace", back_populates="tcompanies")
    legal_representative = relationship("User", foreign_keys=[legal_representative_id])
    created_by_user = relationship("User", foreign_keys=[created_by])
    employees = relationship("Employee", back_populates="tcompany")
    project_companies = relationship("ProjectCompany", back_populates="company")
    technical_documents = relationship("TechnicalDocumentCompany", back_populates="company")
    stakeholders = relationship("Stakeholder", back_populates="company")

    # Contraintes de table
    __table_args__ = (
        # Contrainte unique sur SIRET (global)
        UniqueConstraint('siret', name='unique_tcompany_siret'),
        # Contrainte unique sur (workspace_id, company_name) pour éviter les doublons par workspace
        UniqueConstraint('workspace_id', 'company_name', name='unique_workspace_tcompany_name'),
    )

    def __repr__(self):
        return f"<TCompany(id={self.id}, company_name='{self.company_name}', siret='{self.siret}')>"

    # Propriétés de compatibilité pour la transition (à supprimer après migration complète)
    @property
    def nom_entreprise(self):
        """Propriété de compatibilité - à supprimer après migration"""
        return self.company_name
    
    @property
    def activite(self):
        """Propriété de compatibilité - à supprimer après migration"""
        return self.activity
    
    @property
    def adresse(self):
        """Propriété de compatibilité - à supprimer après migration"""
        return self.address
    
    @property
    def code_postal(self):
        """Propriété de compatibilité - à supprimer après migration"""
        return self.postal_code
    
    @property
    def ville(self):
        """Propriété de compatibilité - à supprimer après migration"""
        return self.city
    
    @property
    def pays(self):
        """Propriété de compatibilité - à supprimer après migration"""
        return self.country
    
    @property
    def telephone(self):
        """Propriété de compatibilité - à supprimer après migration"""
        return self.phone
    
    @property
    def tva_intracommunautaire(self):
        """Propriété de compatibilité - à supprimer après migration"""
        return self.vat_number
    
    @property
    def representant_legal_id(self):
        """Propriété de compatibilité - à supprimer après migration"""
        return self.legal_representative_id

    # Méthodes utilitaires
    def is_siret_valid(self) -> bool:
        """Valide le format du SIRET"""
        if not self.siret:
            return True  # SIRET optionnel
        return len(self.siret) == 14 and self.siret.isdigit()
    
    def get_full_address(self) -> str:
        """Retourne l'adresse complète formatée"""
        parts = []
        if self.address:
            parts.append(self.address)
        if self.postal_code and self.city:
            parts.append(f"{self.postal_code} {self.city}")
        elif self.city:
            parts.append(self.city)
        if self.country and self.country != "France":
            parts.append(self.country)
        return ", ".join(parts)
    
    def get_contact_info(self) -> dict:
        """Retourne les informations de contact structurées"""
        return {
            "phone": self.phone,
            "fax": self.fax,
            "email": self.email,
            "address": self.get_full_address()
        }
