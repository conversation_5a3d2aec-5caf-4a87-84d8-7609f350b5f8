
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="cache-control" content="no-cache, no-store, must-revalidate">
                <meta name="pragma" content="no-cache">
                <meta name="expires" content="0">
                <!-- Cache buster:  -->
                <style>
            /* Reset complet */
            * {
                box-sizing: border-box;
            }

            /* Page de garde sans marges ni headers/footers */
            @page cover {
                size: A4;
                margin: 0mm;
                @top-center { content: none; }
                @bottom-center { content: none; }
                @top-left { content: none; }
                @top-right { content: none; }
                @bottom-left { content: none; }
                @bottom-right { content: none; }
            }

            /* Pages normales avec marges et headers/footers */
            @page normal {
                size: A4;
                margin: 25mm 20mm 25mm 20mm;
                
                @top-center { content: "CCTP"; }
                @bottom-center { content: none; }
                @top-left { content: none; }
                @top-right { content: none; }
                @bottom-left { content: none; }
                @bottom-right { content: none; }
            }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                font-size: 12pt;
                line-height: 1.5;
                color: #333;
                margin: 0;
                padding: 0;
            }

            .project-name {
                font-size: 35pt;
                font-weight: 800;
                color: #222;
                margin: 0 0 8pt 0;
                text-align: center;
            }

            .project-address {
                font-size: 15pt;
                color: #333;
                line-height: 1.4;
                text-align: center;
                margin-bottom: 20pt;
            }

            .cctp-label {
                font-size: 25pt;
                font-weight: bold;
                text-align: center;
                color: #0f766e;
                margin: 20pt 0;
                border: 3px solid #0f766e;
                padding: 10pt;
                border-radius: 8pt;
            }

            h1, h2, h3, h4, h5, h6 {
                color: #0f766e;
                margin-top: 20pt;
                margin-bottom: 12pt;
                font-weight: 600;
                border-bottom: 2px solid #0f766e;
                padding-bottom: 4pt;
            }

            h1 {
                font-size: 20pt;
                font-weight: 700;
                border-bottom: 3px solid #0f766e;
            }
            h2 {
                font-size: 16pt;
                border-bottom: 2px solid #0f766e;
            }
            h3 {
                font-size: 14pt;
                border-bottom: 1px solid #0f766e;
            }

            table {
                border-collapse: collapse;
                width: 100%;
                margin: 16pt 0;
                border: 2px solid #0f766e;
            }

            th {
                background-color: #0f766e;
                color: white;
                font-weight: bold;
                text-align: center;
                border: 1px solid #0f766e;
                padding: 8pt;
            }

            td {
                border: 1px solid #d1d5db;
                padding: 8pt;
                text-align: left;
            }

            p {
                margin-bottom: 12pt;
                text-align: justify;
            }

            ul, ol {
                margin: 12pt 0;
                padding-left: 20pt;
            }

            li {
                margin-bottom: 6pt;
            }

            a, .technical-link {
                color: #0f766e;
                text-decoration: underline;
            }

            /* Classes pour assigner les pages nommées */
            .cover-page {
                page: cover;
                width: 210mm; /* Largeur A4 */
                height: 297mm; /* Hauteur A4 */
                margin: 0;
                padding: 0;
                box-sizing: border-box;
                overflow: hidden;
                page-break-after: always;
            }

            .cover-page-static {
                page: cover;
                width: 210mm;
                height: 297mm;
                margin: 0;
                padding: 0;
                display: flex;
                box-sizing: border-box;
                overflow: hidden;
            }

            .content-page {
                page: normal;
                page-break-before: always;
            }

            .cover-page-static .left-column {
                width: 50%;
                background-color: #0f766e;
                padding: 15px;
                color: white;
                display: flex;
                flex-direction: column;
                box-sizing: border-box;
                overflow: hidden;
            }

            /* Photo du projet dans la colonne gauche */
            .cover-page-static .project-photo-img {
                width: 100%;
                max-height: 400px;
                object-fit: cover;
                border-radius: 4px;
                margin-bottom: 15px;
            }

            .cover-page-static .right-column {
                width: 50%;
                background-color: white;
                padding: 15px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                box-sizing: border-box;
                overflow: hidden;
            }

            .cover-page-static .intervenants-table {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 10px;
                overflow: hidden;
            }

            .cover-page-static .intervenant-row {
                display: flex;
                gap: 8px;
                padding: 8px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 4px;
                align-items: center;
                min-height: 60px;
                box-sizing: border-box;
            }

            .cover-page-static .logo-cell {
                flex-shrink: 0;
                width: 60px;
                height: 60px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .cover-page-static .company-logo {
                width: 60px !important;
                height: 60px !important;
                max-width: 60px !important;
                max-height: 60px !important;
                object-fit: contain !important;
                display: block;
            }

            .cover-page-static .info-cell {
                flex: 1;
                min-width: 0;
                overflow: hidden;
            }

            .cover-page-static .company-name {
                font-size: 15px;
                font-weight: 600;
                margin: 0 0 2px 0;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .cover-page-static .company-details {
                font-size: 10px;
                opacity: 0.9;
                line-height: 1.2;
                word-wrap: break-word;
            }

            .cover-page-static .project-photo {
                width: 100%;
                max-height: 200px;
                object-fit: cover;
                border-radius: 4px;
                margin-bottom: 15px;
            }

            .cover-page-static .project-name {
                font-size: 18px;
                font-weight: 700;
                color: #0f766e;
                margin: 0 0 8px 0;
                text-align: center;
                word-wrap: break-word;
            }

            .cover-page-static .project-address {
                font-size: 12px;
                color: #666;
                text-align: center;
                margin-bottom: 15px;
                word-wrap: break-word;
            }

            /* Logo du workspace */
            .cover-page-static .workspace-logo {
                text-align: center;
                margin-bottom: 20px;
                width: 100%;
                height: 80px;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .cover-page-static .workspace-logo img,
            .cover-page-static .workspace-logo-img {
                width: auto !important;
                height: 80px !important;
                max-width: 200px !important;
                max-height: 80px !important;
                object-fit: contain !important;
                display: block;
            }

            /* Logos MOA */
            .cover-page-static .moa-logo {
                width: 100%;
                max-height: 300px;
                display: flex;
                align-items: center;
                justify-content: center;
                overflow: hidden;
            }

            .cover-page-static .moa-logo img,
            .cover-page-static .moa-logo-img {
                width: 100% !important;
                max-height: 50px !important;
                display: block;
            }

            .cover-page-static .cctp-label {
                font-size: 20px;
                font-weight: 700;
                text-align: center;
                color: #0f766e;
                margin: 15px 0;
                padding: 10px;
                border: 2px solid #0f766e;
                border-radius: 4px;
            }

            .cover-page-static .lot-info {
                text-align: center;
                font-size: 14px;
                color: #333;
                margin-bottom: 15px;
            }

            .cover-page-static table {
                width: 100%;
                border-collapse: collapse;
                font-size: 10px;
                margin-top: 10px;
            }

            .cover-page-static table td {
                border: 1px solid #0f766e;
                padding: 4px 6px;
                text-align: left;
                word-wrap: break-word;
                max-width: 0;
            }

            .cover-page-static table td:first-child {
                background: #f0fdfa;
                font-weight: 600;
                width: 30%;
            }
            </style>
            </head>
            <body>
                
                <div class="cover-page" style="page-break-after: always;">
                    
    <div class="cover-page-static" style="
      display: flex;
      min-height: 100vh;
      font-family: Arial, sans-serif;
      background: white;
      page-break-after: always;
    ">
      <!-- Colonne gauche -->
      <div class="left-column" style="
        flex: 1;
        padding: 20px;
        border-right: 2px solid #0f766e;
      ">
        <!-- Photo du projet en haut de la colonne gauche -->
        
          <div style="text-align: center; margin-bottom: 20px;">
            <img src="http://localhost:8000/uploads/project_photos/project_7_4e47894d3b494f4683b0ce721d83d09d.jpg" alt="Photo du projet" style="
              max-width: 100%;
              max-height: 150px;
              object-fit: cover;
              border-radius: 8px;
              border: 1px solid #e5e7eb;
            " />
          </div>
        

        <h2 style="
          font-size: 24px;
          font-weight: bold;
          color: #0f766e;
          text-align: center;
          margin-bottom: 20px;
          border-bottom: 2px solid #0f766e;
          padding-bottom: 10px;
        ">INTERVENANTS DU LOT</h2>

        <div class="intervenants-table">
          
            <div style="
              display: flex;
              margin-bottom: 20px;
              border: 1px solid #e5e7eb;
              border-radius: 8px;
              overflow: hidden;
            ">
              <div style="
                width: 80px;
                height: 80px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #f8f9fa;
                border-right: 1px solid #e5e7eb;
              ">
                <img src="http://localhost:8000/uploads/tcompany_logos/tcompany_4_94e70fe348714531a5925f57156b4053.webp" alt="Logo" style="max-width: 70px; max-height: 70px; object-fit: contain;" />
              </div>
              <div style="
                flex: 1;
                padding: 12px;
                display: flex;
                flex-direction: column;
                justify-content: center;
              ">
                <div style="
                  font-size: 16px;
                  font-weight: bold;
                  color: #0f766e;
                  margin-bottom: 4px;
                ">Test Architecture SARL</div>
                <div style="
                  font-size: 14px;
                  color: #333;
                  line-height: 1.3;
                  margin-bottom: 2px;
                ">13 rue de la ville du SUD </div>
                <div style="
                  font-size: 14px;
                  color: #666;
                ">0123456789</div>
              </div>
            </div>
          
            <div style="
              display: flex;
              margin-bottom: 20px;
              border: 1px solid #e5e7eb;
              border-radius: 8px;
              overflow: hidden;
            ">
              <div style="
                width: 80px;
                height: 80px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #f8f9fa;
                border-right: 1px solid #e5e7eb;
              ">
                <img src="http://localhost:8000/uploads/tcompany_logos/tcompany_1_e17377309a8e489f8588bb7629852dfe.jpg" alt="Logo" style="max-width: 70px; max-height: 70px; object-fit: contain;" />
              </div>
              <div style="
                flex: 1;
                padding: 12px;
                display: flex;
                flex-direction: column;
                justify-content: center;
              ">
                <div style="
                  font-size: 16px;
                  font-weight: bold;
                  color: #0f766e;
                  margin-bottom: 4px;
                ">Test Entreprise</div>
                <div style="
                  font-size: 14px;
                  color: #333;
                  line-height: 1.3;
                  margin-bottom: 2px;
                ">123 Rue de Test</div>
                <div style="
                  font-size: 14px;
                  color: #666;
                ">0123456789</div>
              </div>
            </div>
          
            <div style="
              display: flex;
              margin-bottom: 20px;
              border: 1px solid #e5e7eb;
              border-radius: 8px;
              overflow: hidden;
            ">
              <div style="
                width: 80px;
                height: 80px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #f8f9fa;
                border-right: 1px solid #e5e7eb;
              ">
                <img src="http://localhost:8000/uploads/tcompany_logos/tcompany_6_81ff6256ec3e449cbdf3ca861d4baaf5.png" alt="Logo" style="max-width: 70px; max-height: 70px; object-fit: contain;" />
              </div>
              <div style="
                flex: 1;
                padding: 12px;
                display: flex;
                flex-direction: column;
                justify-content: center;
              ">
                <div style="
                  font-size: 16px;
                  font-weight: bold;
                  color: #0f766e;
                  margin-bottom: 4px;
                ">Entreprise 4</div>
                <div style="
                  font-size: 14px;
                  color: #333;
                  line-height: 1.3;
                  margin-bottom: 2px;
                ">46 avenue de la place</div>
                <div style="
                  font-size: 14px;
                  color: #666;
                ">0102030405</div>
              </div>
            </div>
          
            <div style="
              display: flex;
              margin-bottom: 20px;
              border: 1px solid #e5e7eb;
              border-radius: 8px;
              overflow: hidden;
            ">
              <div style="
                width: 80px;
                height: 80px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #f8f9fa;
                border-right: 1px solid #e5e7eb;
              ">
                <img src="http://localhost:8000/uploads/tcompany_logos/tcompany_7_eaebd528fbb540f9b80cfe892d1f0b48.png" alt="Logo" style="max-width: 70px; max-height: 70px; object-fit: contain;" />
              </div>
              <div style="
                flex: 1;
                padding: 12px;
                display: flex;
                flex-direction: column;
                justify-content: center;
              ">
                <div style="
                  font-size: 16px;
                  font-weight: bold;
                  color: #0f766e;
                  margin-bottom: 4px;
                ">Entreprise 5</div>
                <div style="
                  font-size: 14px;
                  color: #333;
                  line-height: 1.3;
                  margin-bottom: 2px;
                ">22 rue long fair</div>
                <div style="
                  font-size: 14px;
                  color: #666;
                ">0620892096</div>
              </div>
            </div>
          
        </div>
      </div>

      <!-- Colonne droite -->
      <div class="right-column" style="
        flex: 1;
        padding: 20px;
        display: flex;
        flex-direction: column;
      ">
        <!-- Logo du workspace -->
        

        <!-- Nom du projet -->
        <div style="text-align: center; margin-bottom: 20px;">
          <h3 style="
            font-size: 28px;
            font-weight: bold;
            color: #0f766e;
            margin-bottom: 8px;
          ">Aménagement Restaurant Le Gourmet</h3>
          <div style="
            font-size: 16px;
            color: #333;
            line-height: 1.4;
          ">24 RUE DE LLODI Paris 8ème arrondissement</div>
        </div>

        <!-- Maîtrise d'ouvrage -->
        <div style="
          padding: 16px;
          margin: 20px 0;
          background-color: #f0fdfa;
          border-radius: 8px;
          border: 1px solid #0f766e;
        ">
          <h4 style="
            font-size: 18px;
            font-weight: bold;
            color: #0f766e;
            margin-bottom: 12px;
            text-align: center;
            border-bottom: 2px solid #0f766e;
            padding-bottom: 8px;
          ">MAÎTRISE D'OUVRAGE</h4>
          
          
            <div style="
              display: flex;
              align-items: center;
              gap: 12px;
              margin-bottom: 12px;
            ">
              <div style="
                width: 60px;
                height: 60px;
                display: flex;
                align-items: center;
                justify-content: center;
              ">
                <img src="http://localhost:8000/uploads/tcompany_logos/tcompany_2_c7e48b72b5524682ab4180b82bf4747d.png" alt="Logo MOA" style="width: 60px; height: 60px; object-fit: contain;" />
              </div>
              <div>
                <div style="
                  font-size: 16px;
                  font-weight: bold;
                  color: #0f766e;
                  margin-bottom: 4px;
                ">Mairie du 12 ème</div>
                <div style="
                  font-size: 14px;
                  color: #333;
                  line-height: 1.3;
                ">Adresse non s45 rue du chemin</div>
              </div>
            </div>
          
            <div style="
              display: flex;
              align-items: center;
              gap: 12px;
              
            ">
              <div style="
                width: 60px;
                height: 60px;
                display: flex;
                align-items: center;
                justify-content: center;
              ">
                <img src="http://localhost:8000/uploads/tcompany_logos/tcompany_5_93985db9aa1d4faa838156a8f421090d.jpg" alt="Logo MOA" style="width: 60px; height: 60px; object-fit: contain;" />
              </div>
              <div>
                <div style="
                  font-size: 16px;
                  font-weight: bold;
                  color: #0f766e;
                  margin-bottom: 4px;
                ">ENTREPRISE AJOUT</div>
                <div style="
                  font-size: 14px;
                  color: #333;
                  line-height: 1.3;
                ">46 avenue Du Marechal Foch</div>
              </div>
            </div>
          
        </div>

        <!-- CCTP centré -->
        <div style="
          font-size: 32px;
          font-weight: bold;
          text-align: center;
          color: #0f766e;
          margin: 20px 0;
          border: 3px solid #0f766e;
          padding: 15px;
          border-radius: 8px;
        ">CCTP</div>

        <!-- Informations du lot -->
        <div style="margin-top: auto;">
          <div style="
            font-size: 20px;
            font-weight: bold;
            color: #0f766e;
            text-align: center;
            margin-bottom: 8px;
          ">Lot AFF-2025-470_LOT_01</div>
          <div style="
            font-size: 18px;
            color: #333;
            text-align: center;
            margin-bottom: 20px;
          ">Lot 1 - Aménagement Restaurant - SDB</div>

          <table style="
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #0f766e;
          ">
            <tbody>
              <tr>
                <td style="
                  padding: 8px 12px;
                  border: 1px solid #0f766e;
                  background-color: #f0fdfa;
                  font-weight: bold;
                  color: #0f766e;
                ">Date</td>
                <td style="
                  padding: 8px 12px;
                  border: 1px solid #0f766e;
                  color: #333;
                ">31/07/2025</td>
              </tr>
              <tr>
                <td style="
                  padding: 8px 12px;
                  border: 1px solid #0f766e;
                  background-color: #f0fdfa;
                  font-weight: bold;
                  color: #0f766e;
                ">Phase du lot</td>
                <td style="
                  padding: 8px 12px;
                  border: 1px solid #0f766e;
                  color: #333;
                ">APD</td>
              </tr>
              <tr>
                <td style="
                  padding: 8px 12px;
                  border: 1px solid #0f766e;
                  background-color: #f0fdfa;
                  font-weight: bold;
                  color: #0f766e;
                ">Indice</td>
                <td style="
                  padding: 8px 12px;
                  border: 1px solid #0f766e;
                  color: #333;
                ">A0</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  
                </div>
                <div class="content-page"><div class="technical-editor-content"><h1 class="technical-heading">Table des matières</h1><p class="technical-paragraph"></p><p class="technical-paragraph"></p><h1 class="technical-heading">I - GENERALITES</h1><h2 class="technical-heading">I.1 - OBJET DU DOCUMENT</h2><p class="technical-paragraph">Le présent Cahier des Clauses Techniques Particulières (CCTP) a pour objet de définir les conditions techniques d'exécution des travaux.</p><p class="technical-paragraph"></p><h2 class="technical-heading">I.2 - DOCUMENT TECHNIQUE DE RÉFÉRENCE, NORME ET RÈGLEMENT</h2><p class="technical-paragraph"></p><h2 class="technical-heading">I.3 - ETENDUE DES TRAVAUX</h2><p class="technical-paragraph"></p><h3 class="technical-heading">I.3.1 - PHASAGE DE TRAVAUX</h3><p class="technical-paragraph"></p><h2 class="technical-heading">I.4 - LIMITE DE PRESTATION</h2><p class="technical-paragraph"></p><h2 class="technical-heading">I.5 - SYNTHÈSE AVEC LES AUTRES CORPS D'ÉTAT</h2><p class="technical-paragraph"></p><h2 class="technical-heading">I.6 - OBLIGATION GÉNÉRALE DE L'ENTREPRISE</h2><p class="technical-paragraph"></p><h2 class="technical-heading">I.7 - CHOIX DES MATÉRIELS ET MATÉRIAUX</h2><p class="technical-paragraph"></p><h2 class="technical-heading">I.8 - ESSAIS</h2><p class="technical-paragraph"></p><h2 class="technical-heading">I.9 - PROTECTION DES LOCAUX – HYGIENE SECURITE CHANTIER</h2><p class="technical-paragraph"></p><h3 class="technical-heading">I.9.1 - OBLIGATIONS SPECIFIQUES : COVID-19</h3><p class="technical-paragraph"></p><h2 class="technical-heading">I.10 - GARANTIE</h2><p class="technical-paragraph"></p><h2 class="technical-heading">I.11 - APPROCHE CEE</h2><p class="technical-paragraph"></p><h1 class="technical-heading">II - SPECIFICATION TECHNIQUE GENERALE</h1><p class="technical-paragraph"></p></div></div>
            </body>
            </html>
            