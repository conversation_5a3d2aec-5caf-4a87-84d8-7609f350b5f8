# app/services/chatgpt_service.py
"""
Service pour l'amélioration de texte avec ChatGPT
"""

import asyncio
import logging
from typing import Dict, Optional
from datetime import datetime
import openai
from openai import AsyncOpenAI

from app.core.config import settings
from app.models.document import DocumentType

logger = logging.getLogger(__name__)

class ChatGPTService:
    """Service pour l'amélioration de texte avec l'API OpenAI"""
    
    def __init__(self):
        # Charger la clé API depuis les variables d'environnement
        # Variable d'environnement requise: OPENAI_API_KEY
        self.api_key = settings.OPENAI_API_KEY
        self.model = getattr(settings, 'OPENAI_MODEL', 'gpt-4.1')
        self.max_retries = 3
        self.timeout = 30
        self.client = None

        # Initialiser le client seulement si la clé API est disponible
        if self.api_key and self.api_key != "your-openai-api-key-here":
            try:
                self.client = AsyncOpenAI(api_key=self.api_key)
                logger.info("Service ChatGPT initialisé avec succès")
            except Exception as e:
                logger.warning(f"Impossible d'initialiser le service ChatGPT: {e}")
                self.client = None
        else:
            logger.warning("Clé API OpenAI non configurée - Service ChatGPT désactivé")
            logger.info("Pour activer ChatGPT, définissez la variable d'environnement OPENAI_API_KEY")
        
        # Configuration des prompts par type de document
        self.prompts = {
            DocumentType.CCTP: {
                "Ameliore": """Tu es un expert en rédaction de Cahiers des Clauses Techniques Particulières (CCTP) pour le BTP.
Améliore le texte suivant en respectant ces critères :
- Précision technique et conformité aux normes
- Clarté des spécifications
- Respect du vocabulaire technique BTP
- Structure logique et cohérente

Texte à améliorer : {text}

Contexte supplémentaire : {context}

Réponds uniquement avec le texte amélioré, sans commentaires.""",

                "rephrase": """Tu es un expert en rédaction technique BTP. Reformule le texte suivant pour un CCTP en :
- Utilisant un vocabulaire technique précis
- Améliorant la clarté sans perdre le sens
- Respectant les standards de rédaction CCTP

Texte à reformuler : {text}

Réponds uniquement avec le texte reformulé.""",

                "expand": """Tu es un expert en CCTP. Développe le texte suivant en :
- Ajoutant des détails techniques pertinents
- Précisant les normes et références applicables
- Détaillant les exigences de mise en œuvre
- Conservant la structure existante

Texte à développer : {text}

Contexte : {context}

Réponds uniquement avec le texte développé.""",

                "simplify": """Tu es un expert en rédaction technique. Simplifie le texte suivant pour un CCTP en :
- Gardant l'essentiel technique
- Utilisant des phrases plus courtes
- Conservant la précision nécessaire
- Améliorant la lisibilité

Texte à simplifier : {text}

Réponds uniquement avec le texte simplifié."""
            },
   
        }

    def is_configured(self) -> bool:
        """Vérifie si le service ChatGPT est correctement configuré"""
        return self.client is not None and self.api_key is not None

    def get_configuration_status(self) -> Dict[str, any]:
        """Retourne le statut de configuration du service"""
        return {
            "configured": self.is_configured(),
            "api_key_present": bool(self.api_key and self.api_key != "your-openai-api-key-here"),
            "model": self.model,
            "client_initialized": self.client is not None
        }

    async def enhance_text(
        self, 
        text: str, 
        prompt_type: str, 
        document_type: DocumentType,
        context: Optional[str] = None
    ) -> Dict[str, any]:
        """
        Améliore un texte avec ChatGPT
        
        Args:
            text: Texte à améliorer
            prompt_type: Type de prompt (enhance, rephrase, expand, simplify)
            document_type: Type de document (CCTP, DPGF)
            context: Contexte supplémentaire optionnel
            
        Returns:
            Dict contenant le texte amélioré et les métadonnées
        """
        start_time = datetime.now()

        try:
            # Vérifier que le service est disponible
            if not self.client:
                raise ValueError("Service ChatGPT non disponible - Clé API non configurée")

            # Validation des paramètres
            if not text.strip():
                raise ValueError("Le texte ne peut pas être vide")
            
            if document_type not in self.prompts:
                raise ValueError(f"Type de document non supporté: {document_type}")
            
            if prompt_type not in self.prompts[document_type]:
                raise ValueError(f"Type de prompt non supporté: {prompt_type}")
            
            # Construire le prompt
            prompt_template = self.prompts[document_type][prompt_type]
            prompt = prompt_template.format(
                text=text,
                context=context or "Aucun contexte spécifique"
            )
            
            # Appel à l'API OpenAI avec retry
            enhanced_text = await self._call_openai_with_retry(prompt)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            logger.info(f"Texte amélioré avec succès - Type: {prompt_type}, Document: {document_type}, Temps: {processing_time:.2f}s")
            
            return {
                "original_text": text,
                "enhanced_text": enhanced_text,
                "prompt_type": prompt_type,
                "document_type": document_type,
                "processing_time": processing_time,
                "model_used": self.model,
                "success": True
            }
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Erreur lors de l'amélioration du texte: {str(e)}")
            
            return {
                "original_text": text,
                "enhanced_text": text,  # Retourner le texte original en cas d'erreur
                "prompt_type": prompt_type,
                "document_type": document_type,
                "processing_time": processing_time,
                "model_used": self.model,
                "success": False,
                "error": str(e)
            }

    async def _call_openai_with_retry(self, prompt: str) -> str:
        """
        Appelle l'API OpenAI avec mécanisme de retry
        """
        last_exception = None

        # Log des paramètres de l'appel
        logger.info("🔧 PARAMÈTRES DE L'APPEL OPENAI:")
        logger.info(f"   Modèle: {self.model}")
        logger.info(f"   Max tokens: 2000")
        logger.info(f"   Temperature: 0.3")
        logger.info(f"   Top_p: 0.9")
        logger.info(f"   Timeout: {self.timeout}s")
        logger.info(f"   Max retries: {self.max_retries}")

        for attempt in range(self.max_retries):
            try:
                logger.info(f"🔄 Tentative {attempt + 1}/{self.max_retries} d'appel à OpenAI...")

                response = await asyncio.wait_for(
                    self.client.chat.completions.create(
                        model=self.model,
                        messages=[
                            {
                                "role": "system",
                                "content": "Tu es un expert en rédaction technique pour le secteur du BTP. Tu réponds toujours en français et de manière précise."
                            },
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ],
                        max_tokens=2000,
                        temperature=0.3,  # Peu de créativité pour la précision technique
                        top_p=0.9
                    ),
                    timeout=self.timeout
                )

                # Log des détails de la réponse
                logger.info("✅ RÉPONSE OPENAI REÇUE:")
                logger.info(f"   ID: {response.id}")
                logger.info(f"   Modèle utilisé: {response.model}")
                logger.info(f"   Tokens utilisés: {response.usage.total_tokens if response.usage else 'N/A'}")
                logger.info(f"   Tokens prompt: {response.usage.prompt_tokens if response.usage else 'N/A'}")
                logger.info(f"   Tokens completion: {response.usage.completion_tokens if response.usage else 'N/A'}")
                logger.info(f"   Finish reason: {response.choices[0].finish_reason}")

                content = response.choices[0].message.content.strip()
                logger.info(f"   Longueur du contenu: {len(content)} caractères")

                return content
                
            except asyncio.TimeoutError as e:
                last_exception = e
                logger.warning(f"⏰ Timeout lors de l'appel OpenAI (tentative {attempt + 1}/{self.max_retries})")
                logger.warning(f"   Timeout configuré: {self.timeout}s")
                if attempt < self.max_retries - 1:
                    wait_time = 2 ** attempt
                    logger.info(f"   ⏳ Attente de {wait_time}s avant nouvelle tentative...")
                    await asyncio.sleep(wait_time)  # Backoff exponentiel

            except openai.RateLimitError as e:
                last_exception = e
                logger.warning(f"🚫 Rate limit atteint (tentative {attempt + 1}/{self.max_retries})")
                logger.warning(f"   Détails: {str(e)}")
                if attempt < self.max_retries - 1:
                    wait_time = 5 * (attempt + 1)
                    logger.info(f"   ⏳ Attente de {wait_time}s pour rate limit...")
                    await asyncio.sleep(wait_time)  # Attente plus longue pour rate limit

            except openai.APIError as e:
                last_exception = e
                logger.error(f"❌ Erreur API OpenAI (tentative {attempt + 1}/{self.max_retries}): {str(e)}")
                logger.error(f"   Type d'erreur: {type(e).__name__}")
                if attempt < self.max_retries - 1:
                    wait_time = 2 ** attempt
                    logger.info(f"   ⏳ Attente de {wait_time}s avant nouvelle tentative...")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                last_exception = e
                logger.error(f"💥 Erreur inattendue (tentative {attempt + 1}/{self.max_retries}): {str(e)}")
                logger.error(f"   Type d'erreur: {type(e).__name__}")
                if attempt < self.max_retries - 1:
                    logger.info(f"   ⏳ Attente de 1s avant nouvelle tentative...")
                    await asyncio.sleep(1)

        # Si tous les essais ont échoué
        logger.error("💀 ÉCHEC TOTAL - Tous les appels à OpenAI ont échoué")
        logger.error(f"   Dernière exception: {last_exception}")
        raise last_exception or Exception("Échec de tous les appels à l'API OpenAI")

    def get_available_prompt_types(self, document_type: DocumentType) -> list:
        """Retourne la liste des types de prompts disponibles pour un type de document"""
        return list(self.prompts.get(document_type, {}).keys())

    def get_supported_document_types(self) -> list:
        """Retourne la liste des types de documents supportés"""
        return list(self.prompts.keys())

    def is_available(self) -> bool:
        """Vérifie si le service ChatGPT est disponible"""
        return self.client is not None

    async def ajout_article_cctp(self, article_data: Dict[str, any]) -> Dict[str, any]:
        """
        Génère un article de CCTP avec ChatGPT basé sur les données du formulaire

        Args:
            article_data: Dictionnaire contenant les données du formulaire d'ajout d'article

        Returns:
            Dict contenant l'article généré et les métadonnées
        """
        start_time = datetime.now()

        try:
            # Vérifier que le service est disponible
            if not self.client:
                raise ValueError("Service ChatGPT non disponible - Clé API non configurée")

            # Validation des paramètres requis
            required_fields = ['prestation', 'localisation', 'unite', 'quantite']
            for field in required_fields:
                if not article_data.get(field):
                    raise ValueError(f"Le champ '{field}' est requis")

            # Construire le prompt avec les variables du formulaire
            prompt = self._build_cctp_article_prompt(article_data)

            # Log du prompt envoyé
            logger.info("=" * 80)
            logger.info("🚀 APPEL CHATGPT - GÉNÉRATION D'ARTICLE")
            logger.info("=" * 80)
            logger.info(f"📝 DONNÉES DU FORMULAIRE:")
            for key, value in article_data.items():
                logger.info(f"   {key}: {value}")
            logger.info("-" * 80)
            logger.info(f"📤 PROMPT ENVOYÉ À CHATGPT:")
            logger.info(prompt)
            logger.info("=" * 80)

            # Appel à l'API OpenAI avec retry
            article_content = await self._call_openai_with_retry(prompt)

            # Log du résultat reçu
            logger.info("📥 RÉPONSE REÇUE DE CHATGPT:")
            logger.info("-" * 80)
            logger.info(article_content)
            logger.info("=" * 80)

            processing_time = (datetime.now() - start_time).total_seconds()

            logger.info(f"Article CCTP généré avec succès - Temps: {processing_time:.2f}s")

            return {
                "article_content": article_content,
                "article_data": article_data,
                "processing_time": processing_time,
                "model_used": self.model,
                "success": True
            }

        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Erreur lors de la génération de l'article CCTP: {str(e)}")

            return {
                "article_content": "",
                "article_data": article_data,
                "processing_time": processing_time,
                "model_used": self.model,
                "success": False,
                "error": str(e)
            }

    async def ajout_article_cctp_json(self, article_data: Dict[str, any]) -> Dict[str, any]:
        """
        Génère un article CCTP avec ChatGPT et retourne les données JSON
        """
        try:
            start_time = datetime.now()

            # Construire le prompt avec les variables du formulaire
            prompt = self._build_cctp_article_json_prompt(article_data)

            # Log du prompt envoyé
            logger.info("=" * 80)
            logger.info("🚀 APPEL CHATGPT - GÉNÉRATION D'ARTICLE JSON")
            logger.info("=" * 80)
            logger.info(f"📝 DONNÉES DU FORMULAIRE:")
            for key, value in article_data.items():
                logger.info(f"   {key}: {value}")
            logger.info("-" * 80)
            logger.info(f"📤 PROMPT ENVOYÉ À CHATGPT (JSON):")
            logger.info(prompt)
            logger.info("=" * 80)

            # Appel à l'API OpenAI avec retry
            article_json_str = await self._call_openai_with_retry(prompt)

            # Log du résultat reçu
            logger.info("📥 RÉPONSE JSON REÇUE DE CHATGPT:")
            logger.info("-" * 80)
            logger.info(article_json_str)
            logger.info("=" * 80)

            # Parser le JSON
            try:
                import json
                article_data_parsed = json.loads(article_json_str)
                logger.info("✅ JSON parsé avec succès")

                processing_time = (datetime.now() - start_time).total_seconds()
                logger.info(f"Article CCTP JSON généré avec succès - Temps: {processing_time:.2f}s")

                return {
                    "article_data": article_data_parsed,
                    "processing_time": processing_time,
                    "model_used": self.model,
                    "success": True
                }

            except json.JSONDecodeError as e:
                logger.error(f"❌ Erreur de parsing JSON: {str(e)}")
                logger.error(f"Contenu reçu: {article_json_str}")

                processing_time = (datetime.now() - start_time).total_seconds()

                # Fallback : retourner un objet avec le contenu brut
                return {
                    "article_data": {
                        "error": "JSON invalide",
                        "raw_content": article_json_str,
                        "title": "Article généré",
                        "description": article_json_str
                    },
                    "processing_time": processing_time,
                    "model_used": self.model,
                    "success": False,
                    "error": f"JSON parsing error: {str(e)}"
                }

        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Erreur lors de la génération d'article CCTP JSON: {str(e)}")

            return {
                "article_data": {},
                "processing_time": processing_time,
                "model_used": self.model,
                "success": False,
                "error": str(e)
            }

    def _build_cctp_article_prompt(self, data: Dict[str, any]) -> str:
        """
        Construit le prompt pour la génération d'article CCTP avec format tableau
        """
        # Construire les informations de fourniture
        fourniture_info = []
        if data.get('marque'):
            fourniture_info.append(f"Marque : {data['marque']}")
        if data.get('reference'):
            fourniture_info.append(f"Référence : {data['reference']}")
        if data.get('nature'):
            fourniture_info.append(f"Nature : {data['nature']}")
        if data.get('criteresQualite'):
            fourniture_info.append(f"Critères qualité : {data['criteresQualite']}")
        if data.get('dimensions'):
            fourniture_info.append(f"Dimensions : {data['dimensions']}")
        if data.get('couleur'):
            fourniture_info.append(f"Couleur : {data['couleur']}")

        # Construire les informations de mise en œuvre
        mise_en_oeuvre_info = []
        if data.get('particularite'):
            mise_en_oeuvre_info.append(f"Particularité : {data['particularite']}")
        if data.get('descriptionPose'):
            mise_en_oeuvre_info.append(f"Description pose : {data['descriptionPose']}")
        if data.get('typePose'):
            mise_en_oeuvre_info.append(f"Type de pose : {data['typePose']}")
        if data.get('marquePose'):
            mise_en_oeuvre_info.append(f"Marque matériels pose : {data['marquePose']}")
        if data.get('referencePose'):
            mise_en_oeuvre_info.append(f"Référence pose : {data['referencePose']}")

        # Prompt principal avec format tableau
        prompt = f"""Rédige un article de CCTP bâtiment et organise le résultat sous forme de tableau HTML.

DONNÉES À TRAITER :
- Description : {data.get('prestation', '')}
- Localisation : {data.get('localisation', '')}
- Fourniture : {' | '.join(fourniture_info) if fourniture_info else 'Non spécifiée'}
- Mise en œuvre : {' | '.join(mise_en_oeuvre_info) if mise_en_oeuvre_info else 'Non spécifiée'}"""

        # Ajouter les sections conditionnelles
        if data.get('inclureCriteres', False):
            prompt += "\n- Inclure les critères d'essais, mise en service et réception (sans citer de normes)"

        if data.get('inclureDocs', False):
            prompt += "\n- Inclure les documents techniques à fournir (sans les citer)"

        prompt += f"""
- Unité : {data.get('unite', '')}
- Quantité : {data.get('quantite', '')}

CONSIGNES DE FORMAT TABLEAU PROFESSIONNEL :

1. STRUCTURE HTML PROFESSIONNELLE :
   - Tableau sobre et lisible sans couleurs
   - En-tête avec titre de l'article en gras
   - Corps structuré en sections logiques
   - Pied de tableau pour la quantification

2. STYLE CSS INTÉGRÉ (SANS COULEURS) :
   ```
   <table style="
     border-collapse: collapse;
     width: 100%;
     font-family: Arial, sans-serif;
     font-size: 12px;
     border: 2px solid #000;
     margin: 10px 0;
   ">
   ```

3. ORGANISATION DU CONTENU :
   - EN-TÊTE : Numéro et titre de l'article (centré, en gras)
   - SECTION 1 : DESCRIPTION ET LOCALISATION
   - SECTION 2 : FOURNITURE ET MATÉRIAUX (spécifications techniques)
   - SECTION 3 : MISE EN ŒUVRE (méthodes et matériels)"""

        if data.get('inclureCriteres', False):
            prompt += "\n   - SECTION 4 : CRITÈRES D'ESSAIS ET RÉCEPTION"

        if data.get('inclureDocs', False):
            prompt += "\n   - SECTION 5 : DOCUMENTS TECHNIQUES À FOURNIR"

        prompt += f"""   - PIED : QUANTIFICATION (Unité et Quantité)

4. STYLE DES CELLULES (SOBRE ET PROFESSIONNEL) :
   - En-tête article : font-weight: bold; text-align: center; padding: 15px; border: 2px solid #000; font-size: 14px;
   - Titres sections : font-weight: bold; padding: 12px; width: 25%; border: 1px solid #000; text-align: center;
   - Contenu : padding: 12px; border: 1px solid #000; vertical-align: top; line-height: 1.5; text-align: justify;
   - Pied quantification : font-weight: bold; text-align: center; padding: 10px; border: 2px solid #000;

5. RÈGLES DE CONTENU TECHNIQUE :
   - Rédaction technique précise et professionnelle
   - Phrases complètes et structurées (pas de listes à puces)
   - Spécifications détaillées avec normes et références
   - Vocabulaire BTP professionnel et technique
   - Si information manquante : "Selon DTU en vigueur" ou "Conforme aux normes"
   - Intégrer les caractéristiques techniques dans des phrases fluides

6. STRUCTURE DÉTAILLÉE DU CONTENU :
   - DESCRIPTION : Localisation précise + description technique complète de la prestation
   - FOURNITURE : Marque, référence commerciale, nature du matériau, caractéristiques techniques, dimensions, finition
   - MISE EN ŒUVRE : Méthode de pose, particularités techniques, matériels de fixation, références de pose
   - CRITÈRES : Essais de conformité, vérifications, conditions de réception
   - DOCUMENTS : Plans d'exécution, notices techniques, certificats de conformité
   - QUANTIFICATION : Unité de mesure et quantité prévue

7. EXEMPLE DE MISE EN FORME :
   ```
   [EN-TÊTE] Article X.X - [Prestation]
   [DESCRIPTION] [Contenu détaillé sur plusieurs lignes si nécessaire]
   [FOURNITURE] [Spécifications techniques complètes en phrases]
   [MISE EN ŒUVRE] [Méthodes et matériels de pose détaillés]
   [QUANTIFICATION] Unité : {data.get('unite', '')} - Quantité : {data.get('quantite', '')}
   ```

8. QUALITÉ DU RENDU :
   - Tableau professionnel et lisible
   - Bordures nettes et bien définies
   - Espacement optimal pour la lecture
   - Hiérarchie visuelle claire entre les sections
   - Contenu technique de qualité CCTP

RÉPONDS UNIQUEMENT AVEC LE CODE HTML DU TABLEAU COMPLET, SANS COMMENTAIRES NI EXPLICATIONS."""

        return prompt

    def _build_cctp_article_json_prompt(self, data: Dict[str, any]) -> str:
        """
        Construit le prompt pour la génération d'article CCTP avec réponse JSON
        """
        # Construire les informations de fourniture
        fourniture_info = []
        if data.get('marque'):
            fourniture_info.append(f"Marque : {data['marque']}")
        if data.get('reference'):
            fourniture_info.append(f"Référence : {data['reference']}")
        if data.get('nature'):
            fourniture_info.append(f"Nature : {data['nature']}")
        if data.get('criteresQualite'):
            fourniture_info.append(f"Critères qualité : {data['criteresQualite']}")
        if data.get('dimensions'):
            fourniture_info.append(f"Dimensions : {data['dimensions']}")
        if data.get('couleur'):
            fourniture_info.append(f"Couleur : {data['couleur']}")

        # Construire les informations de mise en œuvre
        mise_en_oeuvre_info = []
        if data.get('particularite'):
            mise_en_oeuvre_info.append(f"Particularité : {data['particularite']}")
        if data.get('descriptionPose'):
            mise_en_oeuvre_info.append(f"Description pose : {data['descriptionPose']}")
        if data.get('typePose'):
            mise_en_oeuvre_info.append(f"Type de pose : {data['typePose']}")
        if data.get('marquePose'):
            mise_en_oeuvre_info.append(f"Marque matériels pose : {data['marquePose']}")
        if data.get('referencePose'):
            mise_en_oeuvre_info.append(f"Référence pose : {data['referencePose']}")

        # Prompt principal avec format JSON
        prompt = f"""Rédige un article de CCTP bâtiment et réponds UNIQUEMENT au format JSON.

DONNÉES À TRAITER :
- Description : {data.get('prestation', '')}
- Localisation : {data.get('localisation', '')}
- Fourniture : {' | '.join(fourniture_info) if fourniture_info else 'Non spécifiée'}
- Mise en œuvre : {' | '.join(mise_en_oeuvre_info) if mise_en_oeuvre_info else 'Non spécifiée'}
- Unité : {data.get('unite', '')}
- Quantité : {data.get('quantite', '')}"""

        # Ajouter les sections conditionnelles
        sections_optionnelles = []
        if data.get('inclureCriteres', False):
            sections_optionnelles.append("criteres_essais")
        if data.get('inclureDocs', False):
            sections_optionnelles.append("documents_techniques")

        prompt += f"""

CONSIGNES DE RÉPONSE JSON :

Réponds UNIQUEMENT avec un objet JSON valide contenant ces champs :

{{
  "article_number": "X.X",
  "title": "Titre de l'article (basé sur la prestation)",
  "description": "Description technique détaillée de la prestation et sa localisation",
  "fourniture": "Spécifications complètes des matériaux, marques, références, caractéristiques",
  "mise_en_oeuvre": "Méthodes de pose, particularités techniques, matériels de fixation"{"," if sections_optionnelles else ""}"""

        if "criteres_essais" in sections_optionnelles:
            prompt += '\n  "criteres_essais": "Critères d\'essais, vérifications et conditions de réception",'

        if "documents_techniques" in sections_optionnelles:
            prompt += '\n  "documents_techniques": "Documents à fournir : plans, notices, certificats",'

        prompt += f"""
  "unite": "{data.get('unite', '')}",
  "quantite": "{data.get('quantite', '')}"
}}

RÈGLES DE CONTENU :
- Rédaction technique précise et professionnelle
- Phrases complètes et structurées (pas de listes à puces)
- Vocabulaire BTP professionnel
- Spécifications détaillées avec normes appropriées
- Si information manquante : "Selon DTU en vigueur" ou "Conforme aux normes"
- Chaque champ doit contenir du texte technique de qualité CCTP

IMPORTANT : Réponds UNIQUEMENT avec le JSON, sans commentaires, sans markdown, sans ```json```."""

        return prompt

# Instance globale du service
chatgpt_service = ChatGPTService()
