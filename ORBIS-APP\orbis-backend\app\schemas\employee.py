# app/schemas/employee.py
from pydantic import BaseModel
from typing import Optional
from datetime import datetime, date
from decimal import Decimal

class EmployeeBase(BaseModel):
    employee_number: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    position: Optional[str] = None
    department: Optional[str] = None
    hire_date: Optional[date] = None
    hourly_rate: Optional[Decimal] = None
    is_active: Optional[bool] = True

class EmployeeCreate(EmployeeBase):
    employee_number: str
    first_name: str
    last_name: str
    workspace_id: int

class EmployeeUpdate(EmployeeBase):
    pass

class EmployeeInDBBase(EmployeeBase):
    id: Optional[int] = None
    workspace_id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Employee(EmployeeInDBBase):
    pass

class TimeEntryBase(BaseModel):
    date: date
    hours: Decimal
    description: Optional[str] = None
    is_overtime: Optional[bool] = False

class TimeEntryCreate(TimeEntryBase):
    employee_id: int
    project_id: Optional[int] = None

class TimeEntry(TimeEntryBase):
    id: Optional[int] = None
    employee_id: int
    project_id: Optional[int] = None
    user_id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class EmployeeAssignment(BaseModel):
    id: Optional[int] = None
    employee_id: int
    user_id: Optional[int] = None
    task_name: str
    description: Optional[str] = None
    assigned_date: date
    due_date: Optional[date] = None
    status: Optional[str] = "assigned"

    class Config:
        from_attributes = True