# app/core/security.py
from datetime import datetime, timedelta
from typing import Any, Union, Optional, Dict, List
from jose import jwt, JWTError
from passlib.context import CryptContext
import secrets
import string
from app.core.config import settings
from app.models.user import UserRole

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Token blacklist (in production, use Redis or database)
token_blacklist = set()

def create_access_token(
    subject: Union[str, Any], 
    expires_delta: Optional[timedelta] = None,
    additional_claims: Optional[Dict[str, Any]] = None
) -> str:
    """Create JWT access token with optional additional claims"""
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    # Generate unique token ID for blacklisting
    jti = generate_token_id()
    
    to_encode = {
        "exp": expire,
        "iat": datetime.utcnow(),
        "sub": str(subject),
        "jti": jti,
        "type": "access"
    }
    
    # Add additional claims (permissions, role, etc.)
    if additional_claims:
        to_encode.update(additional_claims)
    
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

def create_refresh_token(
    subject: Union[str, Any],
    expires_delta: Optional[timedelta] = None
) -> str:
    """Create JWT refresh token"""
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=7)  # 7 days default
    
    jti = generate_token_id()
    
    to_encode = {
        "exp": expire,
        "iat": datetime.utcnow(),
        "sub": str(subject),
        "jti": jti,
        "type": "refresh"
    }
    
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Generate password hash"""
    return pwd_context.hash(password)

def verify_token(token: str, token_type: str = "access") -> Optional[Dict[str, Any]]:
    """Verify JWT token and return payload"""
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        
        # Check token type
        if payload.get("type") != token_type:
            return None
            
        # Check if token is blacklisted
        jti = payload.get("jti")
        if jti and jti in token_blacklist:
            return None
            
        return payload
        
    except JWTError:
        return None

def blacklist_token(token: str) -> bool:
    """Add token to blacklist"""
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        jti = payload.get("jti")
        if jti:
            token_blacklist.add(jti)
            return True
    except JWTError:
        pass
    return False

def generate_token_id() -> str:
    """Generate unique token ID"""
    return ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))

def generate_reset_token() -> str:
    """Generate password reset token"""
    return ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(64))

def get_user_permissions(role: UserRole) -> List[str]:
    """Get permissions list based on user role"""
    role_permissions = {
        UserRole.SUPER_ADMIN: [
            'manage_users', 'manage_projects', 'manage_companies', 'manage_system',
            'view_all_projects', 'view_all_users', 'manage_documents', 'manage_financial',
            'export_data', 'manage_suppliers', 'manage_materials', 'view_analytics'
        ],
        UserRole.ADMIN: [
            'manage_users', 'manage_projects', 'manage_companies',
            'view_all_projects', 'view_all_users', 'manage_documents',
            'manage_financial', 'export_data', 'view_analytics'
        ],
        UserRole.CHEF_PROJET: [
            'create_projects', 'manage_assigned_projects', 'view_team_projects',
            'manage_project_documents', 'view_project_stats', 'manage_employees',
            'create_quotes', 'manage_purchase_orders', 'view_project_financial'
        ],
        UserRole.EMPLOYE: [
            'view_assigned_projects', 'create_time_entries', 'view_own_stats',
            'upload_documents', 'view_project_details', 'update_task_status'
        ],
        UserRole.CLIENT: [
            'view_own_projects', 'view_project_progress', 'download_documents',
            'view_project_timeline', 'view_invoices'
        ]
    }
    
    return role_permissions.get(role, [])

def validate_password_strength(password: str) -> Dict[str, Union[bool, str]]:
    """Validate password strength and return detailed feedback"""
    errors = []
    
    if len(password) < 8:
        errors.append("Password must be at least 8 characters long")
    
    if not any(c.isupper() for c in password):
        errors.append("Password must contain at least one uppercase letter")
    
    if not any(c.islower() for c in password):
        errors.append("Password must contain at least one lowercase letter")
    
    if not any(c.isdigit() for c in password):
        errors.append("Password must contain at least one digit")
    
    if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
        errors.append("Password must contain at least one special character")
    
    return {
        "is_valid": len(errors) == 0,
        "errors": errors,
        "message": "Password is strong" if len(errors) == 0 else "Password does not meet requirements"
    }

def create_token_response(user, access_token: str, refresh_token: Optional[str] = None) -> Dict[str, Any]:
    """Create standardized token response"""
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # in seconds
        "user": {
            "id": user.id,
            "email": user.email,
            "full_name": user.full_name or f"{user.first_name} {user.last_name}",
            "role": user.role,
            "is_active": user.is_active,
            "is_verified": user.is_verified,
            "permissions": get_user_permissions(user.role)
        }
    }