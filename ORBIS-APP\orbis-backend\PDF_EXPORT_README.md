# 📄 Export PDF avec Playwright - ORBIS

## 🎯 Vue d'ensemble

L'export PDF d'ORBIS utilise **Playwright** c<PERSON><PERSON> backend pour générer des PDFs de haute qualité à partir du contenu HTML de l'éditeur technique. Cette approche garantit un rendu parfait et une qualité professionnelle.

## 🏗️ Architecture

```
Frontend (React/Next.js)
    ↓ HTML + Options
Backend (FastAPI + Playwright)
    ↓ PDF généré
Frontend (Téléchargement)
```

### Composants

1. **Backend**: Endpoint `/api/v1/pdf/export-pdf` avec Playwright
2. **Frontend**: Service `PDFExportService` + bouton dans la barre d'outils
3. **Styles**: CSS optimisé pour l'impression A4

## 🚀 Installation

### 1. Backend (FastAPI)

```bash
cd fastapi_template

# Installation automatique
python install_playwright.py

# OU installation manuelle
pip install playwright==1.40.0
playwright install chromium
playwright install-deps chromium
```

### 2. Vérification

```bash
# Test de l'installation
python test_pdf_export.py
```

## 📡 API Backend

### Endpoint: `POST /api/v1/pdf/export-pdf`

**Requête:**
```json
{
  "html": "<div>Contenu HTML...</div>",
  "filename": "document.pdf",
  "title": "Document technique",
  "format": "A4",
  "margin_top": "25mm",
  "margin_bottom": "25mm",
  "margin_left": "20mm",
  "margin_right": "20mm"
}
```

**Réponse:**
```json
{
  "pdf_base64": "JVBERi0xLjQK...",
  "filename": "document.pdf",
  "size_bytes": 245760
}
```

## 🎨 Frontend

### Service PDFExportService

```typescript
import { PDFExportService } from '@/services/pdfExportService'

// Export simple
await PDFExportService.exportToPDF(html, {
  filename: 'mon-document.pdf',
  title: 'Mon Document'
})

// Export avec options avancées
await PDFExportService.exportToPDF(html, {
  filename: 'cctp.pdf',
  title: 'CCTP - Lot Gros Œuvre',
  format: 'A4',
  margins: {
    top: '30mm',
    bottom: '30mm',
    left: '25mm',
    right: '25mm'
  }
})
```

### Hook React

```typescript
import { usePDFExport } from '@/services/pdfExportService'

function MonComposant() {
  const { exportToPDF, isExporting, error } = usePDFExport()
  
  const handleExport = async () => {
    try {
      await exportToPDF(htmlContent, { filename: 'document.pdf' })
    } catch (err) {
      console.error('Erreur export:', err)
    }
  }
  
  return (
    <button onClick={handleExport} disabled={isExporting}>
      {isExporting ? 'Export en cours...' : 'Exporter PDF'}
    </button>
  )
}
```

## 🎨 Styles CSS pour PDF

Le système inclut des styles optimisés pour l'impression:

```css
@page {
  size: A4;
  margin: 0;
}

/* Pages de garde */
.cover-page-static {
  height: 100vh;
  display: flex;
  page-break-after: always;
}

/* Éléments spécifiques */
.project-name {
  font-size: 23pt !important;
  font-weight: bold !important;
}

.cctp-label {
  font-size: 25pt !important;
  text-align: center !important;
}
```

## 🔧 Configuration

### Variables d'environnement

```env
# Frontend (.env.local)
NEXT_PUBLIC_API_URL=http://localhost:8000

# Backend (.env)
# Aucune configuration spéciale requise pour Playwright
```

### Options PDF

| Option | Description | Défaut |
|--------|-------------|--------|
| `format` | Format de page | `A4` |
| `margin_top` | Marge haute | `25mm` |
| `margin_bottom` | Marge basse | `25mm` |
| `margin_left` | Marge gauche | `20mm` |
| `margin_right` | Marge droite | `20mm` |

## 🧪 Tests

### Test automatique

```bash
python test_pdf_export.py
```

### Test manuel via API

```bash
curl -X POST "http://localhost:8000/api/v1/pdf/export-pdf" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "html": "<h1>Test</h1><p>Contenu de test</p>",
    "filename": "test.pdf"
  }'
```

## 🚨 Dépannage

### Erreur: "Chromium not found"

```bash
playwright install chromium
```

### Erreur: "Permission denied"

```bash
# Linux/Mac
sudo playwright install-deps chromium

# Windows (en tant qu'administrateur)
playwright install-deps chromium
```

### Erreur: "Browser launch failed"

Vérifiez les arguments de lancement dans `pdf_export.py`:

```python
browser = await p.chromium.launch(
    headless=True,
    args=[
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu'
    ]
)
```

### PDF vide ou mal formaté

1. Vérifiez que le HTML est valide
2. Attendez le chargement complet: `wait_until="networkidle"`
3. Vérifiez les styles CSS pour l'impression

## 📊 Performance

### Métriques typiques

- **Temps de génération**: 2-5 secondes
- **Taille PDF**: 200KB - 2MB selon le contenu
- **Mémoire**: ~100MB par processus Chromium

### Optimisations

1. **Cache des navigateurs**: Réutiliser les instances Chromium
2. **Compression**: Optimiser les images avant export
3. **Pagination**: Gérer les gros documents par chunks

## 🔒 Sécurité

### Authentification

L'endpoint PDF nécessite une authentification JWT:

```typescript
headers: {
  'Authorization': `Bearer ${token}`
}
```

### Validation

- Taille HTML limitée à 10MB
- Timeout de 30 secondes
- Validation du contenu HTML

## 📈 Monitoring

### Logs

```python
logger.info(f"Export PDF pour utilisateur {user_id}")
logger.info(f"PDF généré - Taille: {len(pdf_buffer)} bytes")
```

### Métriques à surveiller

- Temps de génération PDF
- Taille des fichiers générés
- Erreurs de génération
- Utilisation mémoire Chromium

## 🔄 Mise à jour

### Playwright

```bash
pip install --upgrade playwright
playwright install chromium
```

### Styles CSS

Les styles sont dans `pdf_export.py` fonction `prepare_html_for_pdf()`.

## 📚 Ressources

- [Playwright Python](https://playwright.dev/python/)
- [PDF Generation](https://playwright.dev/python/docs/api/class-page#page-pdf)
- [CSS Print Styles](https://developer.mozilla.org/en-US/docs/Web/CSS/@page)
