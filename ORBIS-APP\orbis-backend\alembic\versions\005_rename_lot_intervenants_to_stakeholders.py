"""Rename lot_intervenants to stakeholders

Revision ID: 005_rename_stakeholders
Revises: 004
Create Date: 2025-01-15 08:22:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '005_rename_stakeholders'
down_revision = '004'
branch_labels = None
depends_on = None


def upgrade():
    """
    Renommer la table lot_intervenants en stakeholders
    Le label français sera "Intervenants"
    """
    
    # Renommer la table
    op.rename_table('lot_intervenants', 'stakeholders')
    
    # Renommer les index
    op.execute('ALTER INDEX ix_lot_intervenants_id RENAME TO ix_stakeholders_id')
    op.execute('ALTER INDEX ix_lot_intervenants_lot_id RENAME TO ix_stakeholders_lot_id')
    op.execute('ALTER INDEX ix_lot_intervenants_company_id RENAME TO ix_stakeholders_company_id')
    
    # Renommer les contraintes de clés étrangères
    op.drop_constraint('lot_intervenants_lot_id_fkey', 'stakeholders', type_='foreignkey')
    op.drop_constraint('lot_intervenants_company_id_fkey', 'stakeholders', type_='foreignkey')
    op.drop_constraint('lot_intervenants_created_by_fkey', 'stakeholders', type_='foreignkey')
    
    # Recréer les contraintes avec les nouveaux noms
    op.create_foreign_key('stakeholders_lot_id_fkey', 'stakeholders', 'lots', ['lot_id'], ['id'])
    op.create_foreign_key('stakeholders_company_id_fkey', 'stakeholders', 'tcompanies', ['company_id'], ['id'])
    op.create_foreign_key('stakeholders_created_by_fkey', 'stakeholders', 'users', ['created_by'], ['id'])


def downgrade():
    """
    Revenir à lot_intervenants
    """
    
    # Supprimer les contraintes actuelles
    op.drop_constraint('stakeholders_lot_id_fkey', 'stakeholders', type_='foreignkey')
    op.drop_constraint('stakeholders_company_id_fkey', 'stakeholders', type_='foreignkey')
    op.drop_constraint('stakeholders_created_by_fkey', 'stakeholders', type_='foreignkey')
    
    # Recréer les contraintes avec les anciens noms
    op.create_foreign_key('lot_intervenants_lot_id_fkey', 'stakeholders', 'lots', ['lot_id'], ['id'])
    op.create_foreign_key('lot_intervenants_company_id_fkey', 'stakeholders', 'tcompanies', ['company_id'], ['id'])
    op.create_foreign_key('lot_intervenants_created_by_fkey', 'stakeholders', 'users', ['created_by'], ['id'])
    
    # Renommer les index
    op.execute('ALTER INDEX ix_stakeholders_id RENAME TO ix_lot_intervenants_id')
    op.execute('ALTER INDEX ix_stakeholders_lot_id RENAME TO ix_lot_intervenants_lot_id')
    op.execute('ALTER INDEX ix_stakeholders_company_id RENAME TO ix_lot_intervenants_company_id')
    
    # Renommer la table
    op.rename_table('stakeholders', 'lot_intervenants')
