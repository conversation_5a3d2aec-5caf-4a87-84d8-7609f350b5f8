# app/api/api_v1/endpoints/quotes.py
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.models.quote import Quote, Quote<PERSON>ine, QuoteTemplate
from app.schemas.quote import Quote as QuoteSchema, QuoteCreate, QuoteUpdate, QuoteLine as QuoteLineSchema, QuoteTemplate as QuoteTemplateSchema

router = APIRouter()

@router.get("/", response_model=List[QuoteSchema])
def read_quotes(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    workspace_id: int = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve quotes.
    """
    if workspace_id:
        deps.get_workspace_access_sync(workspace_id, current_user, db)
        quotes = db.query(Quote).filter(Quote.workspace_id == workspace_id).offset(skip).limit(limit).all()
    else:
        from app.models.workspace import UserWorkspace, Workspace
        user_companies = db.query(UserWorkspace).filter(UserWorkspace.user_id == current_user.id).all()
        company_ids = [uc.workspace_id for uc in user_companies]
        quotes = db.query(Quote).filter(Quote.workspace_id.in_(company_ids)).offset(skip).limit(limit).all()
    
    return quotes

@router.post("/", response_model=QuoteSchema)
def create_quote(
    *,
    db: Session = Depends(deps.get_db),
    quote_in: QuoteCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new quote.
    """
    deps.get_workspace_access_sync(quote_in.workspace_id, current_user, db)
    
    # Check if quote number already exists for this company
    existing = db.query(Quote).filter(
        Quote.workspace_id == quote_in.workspace_id,
        Quote.quote_number == quote_in.quote_number
    ).first()
    if existing:
        raise HTTPException(status_code=400, detail="Quote number already exists for this company")
    
    quote = Quote(**quote_in.dict())
    db.add(quote)
    db.commit()
    db.refresh(quote)
    return quote

@router.get("/{id}", response_model=QuoteSchema)
def read_quote(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get quote by ID.
    """
    quote = db.query(Quote).filter(Quote.id == id).first()
    if not quote:
        raise HTTPException(status_code=404, detail="Quote not found")
    
    deps.get_workspace_access_sync(quote.workspace_id, current_user, db)
    return quote

@router.put("/{id}", response_model=QuoteSchema)
def update_quote(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    quote_in: QuoteUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a quote.
    """
    quote = db.query(Quote).filter(Quote.id == id).first()
    if not quote:
        raise HTTPException(status_code=404, detail="Quote not found")
    
    deps.get_workspace_access_sync(quote.workspace_id, current_user, db)
    
    for field, value in quote_in.dict(exclude_unset=True).items():
        setattr(quote, field, value)
    
    db.add(quote)
    db.commit()
    db.refresh(quote)
    return quote

@router.post("/{id}/lines", response_model=QuoteLineSchema)
def create_quote_line(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    line_in: QuoteLineSchema,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create quote line.
    """
    quote = db.query(Quote).filter(Quote.id == id).first()
    if not quote:
        raise HTTPException(status_code=404, detail="Quote not found")
    
    deps.get_workspace_access_sync(quote.workspace_id, current_user, db)
    
    line = QuoteLine(**line_in.dict(exclude={'id'}), quote_id=id)
    db.add(line)
    db.commit()
    db.refresh(line)
    return line

@router.get("/{id}/lines", response_model=List[QuoteLineSchema])
def read_quote_lines(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get quote lines.
    """
    quote = db.query(Quote).filter(Quote.id == id).first()
    if not quote:
        raise HTTPException(status_code=404, detail="Quote not found")
    
    deps.get_workspace_access_sync(quote.workspace_id, current_user, db)
    
    lines = db.query(QuoteLine).filter(QuoteLine.quote_id == id).all()
    return lines

@router.get("/templates/", response_model=List[QuoteTemplateSchema])
def read_quote_templates(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    workspace_id: int = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve quote templates.
    """
    if workspace_id:
        deps.get_workspace_access_sync(workspace_id, current_user, db)
        templates = db.query(QuoteTemplate).filter(QuoteTemplate.workspace_id == workspace_id).offset(skip).limit(limit).all()
    else:
        from app.models.workspace import UserWorkspace, Workspace
        user_companies = db.query(UserWorkspace).filter(UserWorkspace.user_id == current_user.id).all()
        company_ids = [uc.workspace_id for uc in user_companies]
        templates = db.query(QuoteTemplate).filter(QuoteTemplate.workspace_id.in_(company_ids)).offset(skip).limit(limit).all()
    
    return templates

@router.post("/templates/", response_model=QuoteTemplateSchema)
def create_quote_template(
    *,
    db: Session = Depends(deps.get_db),
    template_in: QuoteTemplateSchema,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new quote template.
    """
    deps.get_workspace_access_sync(template_in.workspace_id, current_user, db)
    
    template = QuoteTemplate(**template_in.dict(exclude={'id'}))
    db.add(template)
    db.commit()
    db.refresh(template)
    return template

@router.post("/{id}/convert-to-purchase-order", response_model=dict)
def convert_quote_to_purchase_order(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Convert quote to purchase order.
    """
    quote = db.query(Quote).filter(Quote.id == id).first()
    if not quote:
        raise HTTPException(status_code=404, detail="Quote not found")
    
    deps.get_workspace_access_sync(quote.workspace_id, current_user, db)
    
    if quote.status != "accepted":
        raise HTTPException(status_code=400, detail="Quote must be accepted to convert to purchase order")
    
    from app.models.purchase_order import PurchaseOrder, PurchaseOrderLine
    import datetime
    
    # Create purchase order
    po = PurchaseOrder(
        workspace_id =quote.workspace_id,
        project_id=quote.project_id,
        supplier_id=quote.supplier_id,
        order_number=f"PO-{quote.quote_number}",
        order_date=datetime.datetime.utcnow(),
        total_amount_ht=quote.total_amount_ht,
        vat_amount=quote.vat_amount,
        total_amount_ttc=quote.total_amount_ttc,
        notes=f"Converted from quote {quote.quote_number}"
    )
    db.add(po)
    db.commit()
    db.refresh(po)
    
    # Create purchase order lines
    quote_lines = db.query(QuoteLine).filter(QuoteLine.quote_id == id).all()
    for line in quote_lines:
        po_line = PurchaseOrderLine(
            purchase_order_id=po.id,
            material_id=line.material_id,
            description=line.description,
            quantity=line.quantity,
            unit_price=line.unit_price,
            total_amount=line.total_amount
        )
        db.add(po_line)
    
    db.commit()
    
    return {"message": "Quote converted to purchase order successfully", "purchase_order_id": po.id}
