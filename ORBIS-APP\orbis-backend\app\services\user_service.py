"""
Service de gestion des utilisateurs avec synchronisation automatique Supabase
"""

import asyncio
from typing import Optional, Dict, Any, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload

from app.models.user import User
from app.models.workspace import UserWorkspace
from app.core.security import get_password_hash, verify_password
from app.services.supabase_service import SupabaseService


class UserService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.supabase_auth = SupabaseService()

    async def create_user(
        self,
        email: str,
        password: str,
        first_name: str = "",
        last_name: str = "",
        role: str = "USER",
        is_active: bool = True,
        auto_sync_supabase: bool = True
    ) -> User:
        """
        Crée un utilisateur et le synchronise automatiquement avec Supabase
        """
        try:
            print(f"🔄 Création utilisateur: {email}")
            
            # 1. Vérifier que l'utilisateur n'existe pas déjà
            existing_user = await self.get_user_by_email(email)
            if existing_user:
                raise ValueError(f"Un utilisateur avec l'email {email} existe déjà")
            
            # 2. Créer l'utilisateur dans Supabase d'abord (si activé)
            supabase_user_id = None
            if auto_sync_supabase:
                try:
                    print(f"📤 Création dans Supabase: {email}")
                    supabase_result = await self.supabase_auth.create_user(
                        email=email,
                        password=password,
                        user_metadata={
                            "first_name": first_name,
                            "last_name": last_name,
                            "role": role
                        }
                    )
                    
                    if supabase_result.get("user"):
                        supabase_user_id = supabase_result["user"]["id"]
                        print(f"✅ Utilisateur créé dans Supabase: {email}")
                    else:
                        print(f"⚠️  Échec création Supabase pour: {email}")
                        
                except Exception as supabase_error:
                    print(f"⚠️  Erreur Supabase pour {email}: {supabase_error}")
                    # Continuer même si Supabase échoue
            
            # 3. Créer l'utilisateur local
            hashed_password = get_password_hash(password)
            local_user = User(
                email=email,
                hashed_password=hashed_password,
                first_name=first_name,
                last_name=last_name,
                role=role,
                is_active=is_active,
                is_verified=True,  # Auto-vérifier pour simplifier
                supabase_user_id=supabase_user_id
            )
            
            self.db.add(local_user)
            await self.db.commit()
            await self.db.refresh(local_user)
            
            print(f"✅ Utilisateur créé localement: {email}")
            return local_user
            
        except Exception as e:
            await self.db.rollback()
            print(f"❌ Erreur création utilisateur {email}: {e}")
            raise

    async def update_user_password(
        self,
        user_id: int,
        new_password: str,
        auto_sync_supabase: bool = True
    ) -> bool:
        """
        Met à jour le mot de passe d'un utilisateur et synchronise avec Supabase
        """
        try:
            # 1. Récupérer l'utilisateur
            user = await self.get_user_by_id(user_id)
            if not user:
                raise ValueError(f"Utilisateur {user_id} non trouvé")
            
            print(f"🔄 Mise à jour mot de passe: {user.email}")
            
            # 2. Mettre à jour dans Supabase (si activé)
            if auto_sync_supabase and user.supabase_user_id:
                try:
                    # Note: Supabase ne permet pas de changer le mot de passe directement
                    # Il faudrait utiliser l'API Admin ou demander un reset
                    print(f"⚠️  Mise à jour Supabase non implémentée pour: {user.email}")
                except Exception as supabase_error:
                    print(f"⚠️  Erreur Supabase pour {user.email}: {supabase_error}")
            
            # 3. Mettre à jour localement
            hashed_password = get_password_hash(new_password)
            await self.db.execute(
                update(User)
                .where(User.id == user_id)
                .values(hashed_password=hashed_password)
            )
            await self.db.commit()
            
            print(f"✅ Mot de passe mis à jour: {user.email}")
            return True
            
        except Exception as e:
            await self.db.rollback()
            print(f"❌ Erreur mise à jour mot de passe: {e}")
            raise

    async def sync_user_to_supabase(self, user_id: int, password: str) -> bool:
        """
        Synchronise un utilisateur existant avec Supabase
        """
        try:
            user = await self.get_user_by_id(user_id)
            if not user:
                raise ValueError(f"Utilisateur {user_id} non trouvé")
            
            print(f"🔄 Synchronisation Supabase: {user.email}")
            
            # Essayer de créer l'utilisateur dans Supabase
            try:
                supabase_result = await self.supabase_auth.create_user(
                    email=user.email,
                    password=password,
                    user_metadata={
                        "first_name": user.first_name or "",
                        "last_name": user.last_name or "",
                        "role": str(user.role) if user.role else "USER"
                    }
                )
                
                if supabase_result.get("user"):
                    # Mettre à jour l'ID Supabase
                    supabase_user_id = supabase_result["user"]["id"]
                    await self.db.execute(
                        update(User)
                        .where(User.id == user_id)
                        .values(supabase_user_id=supabase_user_id)
                    )
                    await self.db.commit()
                    
                    print(f"✅ Utilisateur synchronisé avec Supabase: {user.email}")
                    return True
                else:
                    print(f"❌ Échec synchronisation Supabase: {user.email}")
                    return False
                    
            except Exception as supabase_error:
                error_str = str(supabase_error).lower()
                if "already registered" in error_str or "email_exists" in error_str:
                    print(f"ℹ️  Utilisateur existe déjà dans Supabase: {user.email}")
                    return True
                else:
                    print(f"❌ Erreur Supabase: {supabase_error}")
                    return False
                    
        except Exception as e:
            print(f"❌ Erreur synchronisation: {e}")
            return False

    async def sync_all_users_to_supabase(self, default_password: str = "orbis123!") -> Dict[str, int]:
        """
        Synchronise tous les utilisateurs avec Supabase
        """
        print(f"🔄 Synchronisation de tous les utilisateurs avec Supabase...")
        
        # Récupérer tous les utilisateurs
        result = await self.db.execute(select(User))
        users = result.scalars().all()
        
        stats = {"success": 0, "errors": 0, "already_exists": 0}
        
        for user in users:
            try:
                success = await self.sync_user_to_supabase(user.id, default_password)
                if success:
                    stats["success"] += 1
                else:
                    stats["errors"] += 1
            except Exception as e:
                error_str = str(e).lower()
                if "already registered" in error_str or "email_exists" in error_str:
                    stats["already_exists"] += 1
                else:
                    stats["errors"] += 1
                    
        print(f"📊 Synchronisation terminée: {stats}")
        return stats

    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Récupère un utilisateur par son ID"""
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        return result.scalar_one_or_none()

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Récupère un utilisateur par son email"""
        result = await self.db.execute(
            select(User).where(User.email == email)
        )
        return result.scalar_one_or_none()

    async def get_users_by_workspace(self, workspace_id: int) -> List[User]:
        """Récupère tous les utilisateurs d'un workspace"""
        result = await self.db.execute(
            select(User)
            .join(UserWorkspace)
            .where(UserWorkspace.workspace_id == workspace_id)
            .options(selectinload(User.workspaces))
        )
        return result.scalars().all()

    async def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """
        Authentifie un utilisateur (vérifie email + mot de passe)
        """
        user = await self.get_user_by_email(email)
        if not user:
            return None
            
        if not verify_password(password, user.hashed_password):
            return None
            
        if not user.is_active:
            return None
            
        return user
