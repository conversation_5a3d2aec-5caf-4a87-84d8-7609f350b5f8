# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.venv

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Environment
.env
.env.local
.env.production
.env.staging

# Database
*.db
*.sqlite3
*.sqlite

# Logs
*.log
logs/
*.log.*

# Uploads
uploads/*
!uploads/.gitkeep
uploads/project_photos/*
!uploads/project_photos/.gitkeep

# OS
.DS_Store
Thumbs.db

# Docker
.dockerignore

# Render
.render/

# Test files
test_*.json
auth_token.txt
weasyprint_styles_debug.css
template_config.json

# Development
scripts/test_*.py
scripts/check_*.py
scripts/debug_*.py
scripts/create_test_*.py
scripts/apply_stakeholders_migration*.py
scripts/add_*_fields.py
scripts/fix_*.py
scripts/diagnose_*.py
