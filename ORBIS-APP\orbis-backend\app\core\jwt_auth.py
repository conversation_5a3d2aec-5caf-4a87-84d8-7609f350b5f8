# app/core/jwt_auth.py
"""
Système d'authentification JWT rapide pour les API internes
Utilise Supabase pour l'auth initiale, puis JWT pour les performances
"""

import jwt
from jwt.exceptions import InvalidTokenError, ExpiredSignatureError
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from fastapi import HTTPException, status, Header
from app.core.config import settings

class JWTManager:
    """Gestionnaire JWT pour l'authentification rapide"""
    
    # Clé secrète pour signer les JWT (à mettre dans les variables d'environnement)
    SECRET_KEY = settings.SECRET_KEY or "orbis-jwt-secret-key-change-in-production"
    ALGORITHM = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24  # 24 heures
    
    @classmethod
    def create_access_token(cls, user_data: Dict[str, Any]) -> str:
        """
        Créer un JWT avec les données utilisateur
        """
        to_encode = user_data.copy()
        expire = datetime.utcnow() + timedelta(minutes=cls.ACCESS_TOKEN_EXPIRE_MINUTES)
        to_encode.update({"exp": expire, "iat": datetime.utcnow()})
        
        encoded_jwt = jwt.encode(to_encode, cls.SECRET_KEY, algorithm=cls.ALGORITHM)
        return encoded_jwt
    
    @classmethod
    def verify_token(cls, token: str) -> Dict[str, Any]:
        """
        Vérifier et décoder un JWT
        """
        try:
            payload = jwt.decode(token, cls.SECRET_KEY, algorithms=[cls.ALGORITHM])
            return payload
        except ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token expired"
            )
        except InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
    
    @classmethod
    def create_user_token(cls, user_id: int, email: str, role: str, is_superuser: bool = False) -> str:
        """
        Créer un token pour un utilisateur spécifique
        """
        user_data = {
            "user_id": user_id,
            "email": email,
            "role": role.upper(),
            "is_superuser": is_superuser,
            "token_type": "access"
        }
        return cls.create_access_token(user_data)
    
    @classmethod
    def extract_user_from_token(cls, token: str) -> Dict[str, Any]:
        """
        Extraire les données utilisateur d'un token
        """
        payload = cls.verify_token(token)
        
        return {
            "user_id": payload.get("user_id"),
            "email": payload.get("email"),
            "role": payload.get("role"),
            "is_superuser": payload.get("is_superuser", False)
        }

class FastAuthDependency:
    """
    Dependency FastAPI pour l'authentification rapide via JWT
    """
    
    def __init__(self, require_superuser: bool = False):
        self.require_superuser = require_superuser
    
    def __call__(self, authorization: Optional[str] = Header(None, alias="Authorization")) -> Dict[str, Any]:
        """
        Vérifier l'authentification via JWT
        """
        print(f"🔍 FastAuthDependency - Authorization header: {authorization}")
        if not authorization:
            print("❌ FastAuthDependency - No authorization header")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authorization header missing"
            )
        
        try:
            # Extraire le token du header "Bearer <token>"
            print(f"🔍 FastAuthDependency - Parsing authorization: {authorization}")
            scheme, token = authorization.split()
            if scheme.lower() != "bearer":
                print(f"❌ FastAuthDependency - Invalid scheme: {scheme}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid authentication scheme"
                )
            print(f"🔍 FastAuthDependency - Token extracted: {token[:20]}...")
        except ValueError as e:
            print(f"❌ FastAuthDependency - ValueError parsing header: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authorization header format"
            )

        # Vérifier le token et extraire les données utilisateur
        print(f"🔍 FastAuthDependency - Extracting user from token...")
        try:
            user_data = JWTManager.extract_user_from_token(token)
            print(f"✅ FastAuthDependency - User extracted: {user_data.get('email')}")
        except Exception as e:
            print(f"❌ FastAuthDependency - Error extracting user: {e}")
            raise
        
        # Vérifier les permissions si nécessaire
        if self.require_superuser and not user_data.get("is_superuser"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Superuser access required"
            )
        
        return user_data

# Instances prêtes à utiliser
require_auth = FastAuthDependency(require_superuser=False)
require_superuser = FastAuthDependency(require_superuser=True)
