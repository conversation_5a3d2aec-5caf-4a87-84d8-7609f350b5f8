#!/usr/bin/env python3
"""
Database seeding script for ORBIS Suivi Travaux
Run this script to populate the database with test data
"""

import os
import sys
import asyncio
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.core.database import init_db
from app.core.seed_data import create_seed_data

async def main():
    """Initialize database and create seed data"""
    try:
        print("🔧 Initializing database...")
        await init_db()
        print("✅ Database tables created successfully")
        
        print("\n🌱 Creating seed data...")
        await create_seed_data()
        print("\n✅ Database seeding completed successfully!")
        
        print("\n📋 Test Login Credentials:")
        print("   Admin: <EMAIL> / admin123")
        print("   Chef de Projet: <EMAIL> / chef123")
        print("   Conducteur: <EMAIL> / conducteur123")
        print("   Ouvrier: <EMAIL> / ouvrier123")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
