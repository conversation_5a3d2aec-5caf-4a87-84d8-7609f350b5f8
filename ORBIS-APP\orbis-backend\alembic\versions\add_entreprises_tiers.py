"""Add entreprises_tiers table

⚠️ OBSOLETE: Cette migration est remplacée par TCompanies
Cette migration est conservée pour l'historique mais ne doit plus être utilisée.

Revision ID: add_entreprises_tiers
Revises: create_project_company
Create Date: 2024-12-19 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_entreprises_tiers'
down_revision = 'create_project_company'
branch_labels = None
depends_on = None


def upgrade():
    """Créer la table entreprises_tiers"""
    op.create_table(
        'entreprises_tiers',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('nom_entreprise', sa.String(length=255), nullable=False),
        sa.Column('activite', sa.String(length=255), nullable=True),
        sa.Column('adresse', sa.Text(), nullable=True),
        sa.Column('code_postal', sa.String(length=10), nullable=True),
        sa.Column('ville', sa.String(length=100), nullable=True),
        sa.Column('pays', sa.String(length=100), nullable=True),
        sa.Column('telephone', sa.String(length=20), nullable=True),
        sa.Column('fax', sa.String(length=20), nullable=True),
        sa.Column('email', sa.String(length=255), nullable=True),
        sa.Column('siret', sa.String(length=14), nullable=True),
        sa.Column('tva_intracommunautaire', sa.String(length=20), nullable=True),
        sa.Column('representant_legal_id', sa.Integer(), nullable=True),
        sa.Column('company_id', sa.Integer(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ),
        sa.ForeignKeyConstraint(['representant_legal_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Index pour améliorer les performances
    op.create_index(op.f('ix_entreprises_tiers_id'), 'entreprises_tiers', ['id'], unique=False)
    op.create_index(op.f('ix_entreprises_tiers_nom_entreprise'), 'entreprises_tiers', ['nom_entreprise'], unique=False)
    op.create_index(op.f('ix_entreprises_tiers_email'), 'entreprises_tiers', ['email'], unique=False)
    op.create_index(op.f('ix_entreprises_tiers_siret'), 'entreprises_tiers', ['siret'], unique=True)
    op.create_index(op.f('ix_entreprises_tiers_company_id'), 'entreprises_tiers', ['company_id'], unique=False)


def downgrade():
    """Supprimer la table entreprises_tiers"""
    op.drop_index(op.f('ix_entreprises_tiers_company_id'), table_name='entreprises_tiers')
    op.drop_index(op.f('ix_entreprises_tiers_siret'), table_name='entreprises_tiers')
    op.drop_index(op.f('ix_entreprises_tiers_email'), table_name='entreprises_tiers')
    op.drop_index(op.f('ix_entreprises_tiers_nom_entreprise'), table_name='entreprises_tiers')
    op.drop_index(op.f('ix_entreprises_tiers_id'), table_name='entreprises_tiers')
    op.drop_table('entreprises_tiers')
