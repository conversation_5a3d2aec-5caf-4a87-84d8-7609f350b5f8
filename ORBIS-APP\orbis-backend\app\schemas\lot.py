# app/schemas/lot.py
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from app.models.lot import LotPhase
from app.schemas.tcompany import TCompanyResponse
from app.schemas.stakeholder import StakeholderResponse
from app.schemas.technical_document import ProjectSimple

class LotBase(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    description: Optional[str] = None
    current_phase: Optional[LotPhase] = LotPhase.ESQ
    photo_url: Optional[str] = None
    photo_filename: Optional[str] = None

class LotCreate(LotBase):
    name: str = Field(..., min_length=1, max_length=255)
    project_id: int = Field(..., gt=0)
    code: Optional[str] = None  # Sera généré automatiquement si non fourni

class LotUpdate(LotBase):
    pass

class LotPhaseValidation(BaseModel):
    phase: LotPhase
    validated: bool = True

class LotIntervenantBase(BaseModel):
    role: Optional[str] = Field(None, max_length=100)
    is_active: Optional[bool] = True

class LotIntervenantCreate(LotIntervenantBase):
    company_id: Optional[int] = Field(None, gt=0)  # Si None, créer une nouvelle TCompany
    company_data: Optional[dict] = None  # Données pour créer une nouvelle entreprise
    role: Optional[str] = Field(None, max_length=100)

class LotIntervenantUpdate(LotIntervenantBase):
    pass

class LotIntervenantResponse(LotIntervenantBase):
    id: int
    lot_id: int
    company_id: int
    company: Optional[TCompanyResponse] = None
    created_at: datetime
    
    class Config:
        from_attributes = True

class LotDocumentBase(BaseModel):
    phase: Optional[LotPhase] = None

class LotDocumentCreate(LotDocumentBase):
    document_id: int = Field(..., gt=0)
    lot_id: int = Field(..., gt=0)

class LotDocumentResponse(LotDocumentBase):
    id: int
    lot_id: int
    document_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

class LotInDBBase(LotBase):
    id: Optional[int] = None
    project_id: Optional[int] = None
    workspace_id: Optional[int] = None
    
    # Phases validées
    esq_validated: Optional[bool] = False
    esq_validated_at: Optional[datetime] = None
    esq_validated_by: Optional[int] = None
    
    apd_validated: Optional[bool] = False
    apd_validated_at: Optional[datetime] = None
    apd_validated_by: Optional[int] = None
    
    prodce_validated: Optional[bool] = False
    prodce_validated_at: Optional[datetime] = None
    prodce_validated_by: Optional[int] = None
    
    exe_validated: Optional[bool] = False
    exe_validated_at: Optional[datetime] = None
    exe_validated_by: Optional[int] = None
    
    # Métadonnées
    is_active: Optional[bool] = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: Optional[int] = None

    class Config:
        from_attributes = True

class LotResponse(LotInDBBase):
    """Schéma de réponse simplifié pour les API"""
    stakeholders: Optional[List[StakeholderResponse]] = []
    documents: Optional[List[LotDocumentResponse]] = []
    project: Optional[ProjectSimple] = None
    
    # Alias pour compatibilité (à supprimer après migration complète)
    @property
    def intervenants(self):
        """Propriété de compatibilité - à supprimer après migration"""
        return self.stakeholders
    
    # Propriétés calculées
    @property
    def phase_progress(self) -> dict:
        """Retourne le progrès des phases"""
        return {
            "ESQ": self.esq_validated or False,
            "APD": self.apd_validated or False,
            "PRODCE": self.prodce_validated or False,
            "EXE": self.exe_validated or False
        }
    
    @property
    def next_phase(self) -> Optional[LotPhase]:
        """Retourne la prochaine phase à valider"""
        if not self.esq_validated:
            return LotPhase.ESQ
        elif not self.apd_validated:
            return LotPhase.APD
        elif not self.prodce_validated:
            return LotPhase.PRODCE
        elif not self.exe_validated:
            return LotPhase.EXE
        return None

class Lot(LotInDBBase):
    """Schéma complet du lot avec ses relations"""
    intervenants: Optional[List[LotIntervenantResponse]] = []
    documents: Optional[List[LotDocumentResponse]] = []

# Schémas pour les statistiques et rapports
class LotStats(BaseModel):
    total_active: int
    total_inactive: int
    by_phase: dict  # {"ESQ": 5, "APD": 3, ...}

class LotPhaseHistory(BaseModel):
    """Historique des validations de phases"""
    lot_id: int
    phase: LotPhase
    validated: bool
    validated_at: Optional[datetime]
    validated_by: Optional[int]
    validator_name: Optional[str] = None

# Schémas pour les opérations en lot
class BulkLotOperation(BaseModel):
    lot_ids: List[int] = Field(..., min_items=1)
    operation: str  # "validate_phase", "change_phase", "add_intervenant", etc.
    parameters: Optional[dict] = None

class BulkLotResponse(BaseModel):
    success_count: int
    error_count: int
    errors: Optional[List[dict]] = None
    updated_lots: Optional[List[int]] = None
