# Nettoyage ORBIS Backend pour Production - PowerShell Script
# Ce script nettoie le dossier des fichiers de test et debug pour la production

Write-Host "🧹 Nettoyage ORBIS Backend pour Production" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Définir le dossier racine
$rootDir = Get-Location

# 1. Supprimer les fichiers de test/debug
Write-Host "`n📁 Suppression des fichiers de test/debug..." -ForegroundColor Yellow

$filesToRemove = @(
    "test_lots_results.json",
    "test_user_credentials.json",
    "auth_token.txt",
    "weasyprint_styles_debug.css",
    "template_config.json"
)

foreach ($file in $filesToRemove) {
    $filePath = Join-Path $rootDir $file
    if (Test-Path $filePath) {
        Remove-Item $filePath -Force
        Write-Host "✅ Supprimé: $file" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Non trouvé: $file" -ForegroundColor Yellow
    }
}

# 2. Nettoyer le dossier scripts
Write-Host "`n📁 Nettoyage du dossier scripts..." -ForegroundColor Yellow

$scriptsDir = Join-Path $rootDir "scripts"
$scriptsToRemove = @(
    "test_*.py",
    "check_*.py",
    "debug_*.py",
    "create_test_*.py",
    "apply_stakeholders_migration*.py",
    "add_*_fields.py",
    "fix_*.py",
    "diagnose_*.py",
    "audit_database_tables.py",
    "auto_sync_supabase.py",
    "check_companies_table.py",
    "check_database_structure.py",
    "check_db_structure.py",
    "check_enum_values.py",
    "check_enums.py",
    "check_jeremy_user.py",
    "check_migration_status.py",
    "check_tables.py",
    "clean_user_data.py",
    "create_confirmed_user.py",
    "create_jeremy_admin_jwt.py",
    "create_orbis_user.py",
    "create_rbac_system.py",
    "create_sample_technical_documents.py",
    "create_tables_simple.py",
    "create_test_enterprises.py",
    "create_test_user.py",
    "debug_user_access.py",
    "diagnose_test_user.py",
    "enable_rls.py",
    "final_seed.py",
    "fix_technical_documents_simple.py",
    "fix_technical_documents_table.py",
    "fix_user_role_uppercase.py",
    "get_auth_token.py",
    "install_playwright.py",
    "migrate_to_rbac.py",
    "minimal_seed.py",
    "quick_db_test.py",
    "quick_table_audit.py",
    "reset_all_passwords.py",
    "simple_db_test.py",
    "simple_orbis_setup.py",
    "simple_seed.py",
    "sync_users_to_supabase.py",
    "test_admin_api.py",
    "test_api_create_document.py",
    "test_app_models.py",
    "test_article_generation.py",
    "test_auth_403_debug.py",
    "test_auth_bypass.py",
    "test_auth.py",
    "test_complete_auth.py",
    "test_custom_headers_footers.py",
    "test_dashboard_api.py",
    "test_db_connection.py",
    "test_db.py",
    "test_fast_auth.py",
    "test_final_auth.py",
    "test_frontend_backend.py",
    "test_full_auth_flow.py",
    "test_invitation_system.py",
    "test_login_jeremy.py",
    "test_lots_creation.py",
    "test_lots_endpoint.py",
    "test_lots_retrieval.py",
    "test_message_flow.py",
    "test_migration.py",
    "test_openai_config.py",
    "test_orbis_auth.py",
    "test_pdf_endpoint.py",
    "test_pdf_export.py",
    "test_puppeteer.py",
    "test_quick_auth.py",
    "test_rbac_system.py",
    "test_stakeholders_implementation.py",
    "test_supabase_auth.py",
    "test_technical_document_creation.py",
    "test_technical_documents.py",
    "test_weasyprint_api.py",
    "test_weasyprint_endpoint.py",
    "test_weasyprint_orbis.py",
    "test_weasyprint_simple.py",
    "test_workspace_endpoints.py",
    "test_workspace_models.py",
    "test_workspace_schemas.py",
    "update_foreign_keys.py",
    "verify_migration.py"
)

# Scripts à garder
$scriptsToKeep = @(
    "create_admin_user.py",
    "create_orbis_user.py",
    "seed_db.py",
    "start_server.py",
    "run_migration.py"
)

Write-Host "`n🗑️  Suppression des scripts de développement..." -ForegroundColor Red
$removedCount = 0

foreach ($pattern in $scriptsToRemove) {
    $files = Get-ChildItem -Path $scriptsDir -Filter $pattern -File -ErrorAction SilentlyContinue
    foreach ($file in $files) {
        if ($scriptsToKeep -notcontains $file.Name) {
            Remove-Item $file.FullName -Force
            Write-Host "❌ Supprimé: $($file.Name)" -ForegroundColor Red
            $removedCount++
        }
    }
}

Write-Host "`n✅ Scripts gardés pour la production:" -ForegroundColor Green
foreach ($script in $scriptsToKeep) {
    Write-Host "   📄 $script" -ForegroundColor Green
}

# 3. Vérifier l'espace libéré
Write-Host "`n📊 Résumé du nettoyage:" -ForegroundColor Cyan
Write-Host "   - Fichiers de test supprimés: $($filesToRemove.Count)" -ForegroundColor Yellow
Write-Host "   - Scripts de développement supprimés: $removedCount" -ForegroundColor Yellow
Write-Host "   - Scripts gardés: $($scriptsToKeep.Count)" -ForegroundColor Green

# 4. Vérification finale
Write-Host "`n🔍 Vérification de l'intégrité..." -ForegroundColor Blue
$remainingScripts = Get-ChildItem -Path $scriptsDir -Filter "*.py" -File | Where-Object { $scriptsToKeep -contains $_.Name }
Write-Host "   ✅ Scripts de production présents: $($remainingScripts.Count)" -ForegroundColor Green

Write-Host "`n🎉 Nettoyage terminé ! Le projet est prêt pour la production." -ForegroundColor Green
Write-Host "💡 Pour vérifier: python -m uvicorn app.main:app --reload" -ForegroundColor Cyan
