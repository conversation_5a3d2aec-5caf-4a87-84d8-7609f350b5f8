#!/usr/bin/env python3
"""
Script pour ajouter le champ supabase_user_id à la table users
"""

import asyncio
import asyncpg
from datetime import datetime

# Configuration
DATABASE_URL = "**************************************************************************************************/postgres"


async def add_supabase_user_id_column():
    """Ajouter la colonne supabase_user_id à la table users"""
    print("🔧 Ajout de la colonne supabase_user_id...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Vérifier si la colonne existe déjà
        column_exists = await conn.fetchval("""
            SELECT COUNT(*) 
            FROM information_schema.columns 
            WHERE table_name = 'users' AND column_name = 'supabase_user_id'
        """)
        
        if column_exists > 0:
            print("   ✅ La colonne supabase_user_id existe déjà")
            return True
        
        # Ajouter la colonne
        await conn.execute("""
            ALTER TABLE users 
            ADD COLUMN supabase_user_id VARCHAR(255) UNIQUE
        """)
        
        print("   ✅ Colonne supabase_user_id ajoutée")
        
        # Créer un index pour les performances
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_users_supabase_user_id 
            ON users(supabase_user_id)
        """)
        
        print("   ✅ Index créé")
        
        # Rendre le champ hashed_password optionnel
        await conn.execute("""
            ALTER TABLE users 
            ALTER COLUMN hashed_password DROP NOT NULL
        """)
        
        print("   ✅ Champ hashed_password rendu optionnel")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'ajout de la colonne: {e}")
        return False
    finally:
        await conn.close()


async def test_migration():
    """Tester la migration"""
    print("\n🧪 Test de la migration...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Vérifier la structure de la table
        columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'users'
            ORDER BY ordinal_position
        """)
        
        print("   📋 Structure de la table users:")
        for col in columns:
            nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
            print(f"      - {col['column_name']}: {col['data_type']} ({nullable})")
        
        # Vérifier que supabase_user_id est présent
        supabase_col = next((col for col in columns if col['column_name'] == 'supabase_user_id'), None)
        if supabase_col:
            print("   ✅ Colonne supabase_user_id confirmée")
            return True
        else:
            print("   ❌ Colonne supabase_user_id manquante")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur lors du test: {e}")
        return False
    finally:
        await conn.close()


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Migration supabase_user_id")
    print("="*50)
    
    # Ajouter la colonne
    migration_success = await add_supabase_user_id_column()
    
    if migration_success:
        # Tester la migration
        test_success = await test_migration()
        
        if test_success:
            print("\n🎉 Migration terminée avec succès!")
            print("✅ La table users est prête pour la synchronisation Supabase")
            return True
    
    print("\n❌ Échec de la migration")
    return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
