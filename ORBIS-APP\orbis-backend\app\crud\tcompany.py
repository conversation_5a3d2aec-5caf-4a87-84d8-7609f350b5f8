"""
CRUD operations for TCompany (Entreprises)
"""

from typing import List, Optional
from sqlalchemy.orm import Session
from app.models.tcompany import TCompany
from app.schemas.tcompany import TCompanyCreate, TCompanyUpdate

def create_tcompany(db: Session, tcompany_data: TCompanyCreate, workspace_id: int = 1) -> TCompany:
    """Créer une nouvelle entreprise"""
    db_tcompany = TCompany(
        company_name=tcompany_data.company_name,
        activity=getattr(tcompany_data, 'activity', None),
        email=getattr(tcompany_data, 'email', None),
        phone=getattr(tcompany_data, 'phone', None),
        address=getattr(tcompany_data, 'address', None),
        postal_code=getattr(tcompany_data, 'postal_code', None),
        city=getattr(tcompany_data, 'city', None),
        country=getattr(tcompany_data, 'country', 'France'),
        siret=getattr(tcompany_data, 'siret', None),
        fax=getattr(tcompany_data, 'fax', None),
        vat_number=getattr(tcompany_data, 'vat_number', None),
        workspace_id=workspace_id
    )
    
    db.add(db_tcompany)
    db.commit()
    db.refresh(db_tcompany)
    return db_tcompany

def get_tcompany(db: Session, tcompany_id: int) -> Optional[TCompany]:
    """Récupérer une entreprise par ID"""
    return db.query(TCompany).filter(TCompany.id == tcompany_id).first()

def get_tcompanies(db: Session, skip: int = 0, limit: int = 100) -> List[TCompany]:
    """Récupérer la liste des entreprises"""
    return db.query(TCompany).offset(skip).limit(limit).all()

def update_tcompany(db: Session, tcompany_id: int, tcompany_data: TCompanyUpdate) -> Optional[TCompany]:
    """Mettre à jour une entreprise"""
    db_tcompany = get_tcompany(db, tcompany_id)
    if not db_tcompany:
        return None
    
    update_data = tcompany_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_tcompany, field, value)
    
    db.commit()
    db.refresh(db_tcompany)
    return db_tcompany

def delete_tcompany(db: Session, tcompany_id: int) -> Optional[TCompany]:
    """Supprimer une entreprise"""
    db_tcompany = get_tcompany(db, tcompany_id)
    if not db_tcompany:
        return None
    
    db.delete(db_tcompany)
    db.commit()
    return db_tcompany
