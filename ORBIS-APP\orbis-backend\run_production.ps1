# Script de Production ORBIS Backend - PowerShell
# Script complet pour nettoyer, vérifier et démarrer l'application en production

param(
    [switch]$SkipCleanup = $false,
    [switch]$SkipVerify = $false,
    [switch]$DockerMode = $false,
    [switch]$DevMode = $false
)

Write-Host @"
╔══════════════════════════════════════════════════════════════════════════════════════╗
║                        🚀 ORBIS Backend - Script de Production                      ║
║                           PowerShell - Windows 10/11                                 ║
╚══════════════════════════════════════════════════════════════════════════════════════╝
"@ -ForegroundColor Cyan

# Fonction pour afficher les étapes
function Show-Step {
    param([string]$Message, [string]$Color = "Cyan")
    Write-Host "`n$Message" -ForegroundColor $Color
    Write-Host ("=" * $Message.Length) -ForegroundColor $Color
}

# Fonction pour exécuter avec vérification d'erreur
function Invoke-SafeCommand {
    param([scriptblock]$Command, [string]$SuccessMessage, [string]$ErrorMessage)
    try {
        & $Command
        if ($SuccessMessage) {
            Write-Host "✅ $SuccessMessage" -ForegroundColor Green
        }
        return $true
    } catch {
        Write-Host "❌ $ErrorMessage" -ForegroundColor Red
        Write-Host "   Erreur: $_" -ForegroundColor Red
        return $false
    }
}

# 1. NETTOYAGE
if (-not $SkipCleanup) {
    Show-Step "🧹 ÉTAPE 1: Nettoyage pour Production" "Yellow"
    
    if (Test-Path ".\clean_for_production.ps1") {
        Write-Host "Exécution du script de nettoyage..." -ForegroundColor Yellow
        & ".\clean_for_production.ps1"
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "⚠️  Le nettoyage a rencontré des problèmes" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ Script de nettoyage non trouvé: clean_for_production.ps1" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "⏭️  Nettoyage ignoré (--SkipCleanup)" -ForegroundColor Gray
}

# 2. VÉRIFICATION
if (-not $SkipVerify) {
    Show-Step "🔍 ÉTAPE 2: Vérification de l'intégrité" "Blue"
    
    if (Test-Path ".\verify_production.ps1") {
        Write-Host "Exécution de la vérification..." -ForegroundColor Blue
        & ".\verify_production.ps1"
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "⚠️  La vérification a détecté des problèmes" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ Script de vérification non trouvé: verify_production.ps1" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "⏭️  Vérification ignorée (--SkipVerify)" -ForegroundColor Gray
}

# 3. INSTALLATION DES DÉPENDANCES
Show-Step "📦 ÉTAPE 3: Installation des dépendances" "Magenta"

$pythonCheck = Invoke-SafeCommand {
    python --version
} "Python trouvé" "Python non installé ou non dans le PATH"

if (-not $pythonCheck) {
    exit 1
}

$pipCheck = Invoke-SafeCommand {
    pip --version
} "Pip trouvé" "Pip non disponible"

if (-not $pipCheck) {
    exit 1
}

Write-Host "Installation des packages Python..." -ForegroundColor Magenta
$depsInstall = Invoke-SafeCommand {
    pip install -r requirements.txt
} "Dépendances Python installées" "Erreur lors de l'installation des dépendances"

if (-not $depsInstall) {
    exit 1
}

# 4. CONFIGURATION ENVIRONMENT
Show-Step "⚙️ ÉTAPE 4: Configuration de l'environnement" "Cyan"

if (-not (Test-Path ".env")) {
    Write-Host "Création du fichier .env à partir de .env.example..." -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
    Write-Host "⚠️  Veuillez éditer le fichier .env avec vos configurations" -ForegroundColor Yellow
    Write-Host "   Variables importantes à configurer:" -ForegroundColor Cyan
    Write-Host "   - SUPABASE_URL" -ForegroundColor White
    Write-Host "   - SUPABASE_ANON_KEY" -ForegroundColor White
    Write-Host "   - OPENAI_API_KEY (optionnel)" -ForegroundColor White
}

# 5. DÉMARRAGE
Show-Step "🚀 ÉTAPE 5: Démarrage de l'application" "Green"

if ($DockerMode) {
    Write-Host "Mode Docker activé (--DockerMode)" -ForegroundColor Green
    
    $dockerCheck = Invoke-SafeCommand {
        docker --version
    } "Docker trouvé" "Docker non installé"
    
    if ($dockerCheck) {
        Write-Host "Construction et démarrage avec Docker Compose..." -ForegroundColor Green
        Invoke-SafeCommand {
            docker-compose up --build
        } "Application démarrée avec Docker" "Erreur Docker"
    }
} elseif ($DevMode) {
    Write-Host "Mode développement activé (--DevMode)" -ForegroundColor Yellow
    
    Write-Host "Démarrage du serveur de développement..." -ForegroundColor Yellow
    Invoke-SafeCommand {
        python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    } "Serveur de développement démarré" "Erreur lors du démarrage"
} else {
    Write-Host "Mode production activé" -ForegroundColor Green
    
    Write-Host "Démarrage du serveur en production..." -ForegroundColor Green
    Invoke-SafeCommand {
        python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
    } "Serveur de production démarré" "Erreur lors du démarrage"
}

# 6. INSTRUCTIONS POST-DÉMARRAGE
Show-Step "📋 INSTRUCTIONS" "White"

Write-Host @"
╔══════════════════════════════════════════════════════════════════════════════════════╗
║                          🎯 Instructions de Production                                 ║
╠══════════════════════════════════════════════════════════════════════════════════════╣
║                                                                                      ║
║  📖 Documentation API:                                                             ║
║     http://localhost:8000/docs                                                      ║
║                                                                                      ║
║  🔐 Créer un administrateur:                                                        ║
║     python scripts/create_admin_user.py                                              ║
║                                                                                      ║
║  🌱 Seeder la base de données:                                                      ║
║     python scripts/seed_db.py                                                        ║
║                                                                                      ║
║  🐳 Docker (optionnel):                                                             ║
║     docker-compose up --build                                                        ║
║                                                                                      ║
╚══════════════════════════════════════════════════════════════════════════════════════╝
"@ -ForegroundColor Cyan

Write-Host "`n✅ Script de production terminé !" -ForegroundColor Green
Write-Host "💡 Utilisez --help pour voir toutes les options" -ForegroundColor Gray
Write-Host "   Exemples:" -ForegroundColor Gray
Write-Host "   .\run_production.ps1 -DockerMode" -ForegroundColor White
Write-Host "   .\run_production.ps1 -DevMode" -ForegroundColor White
Write-Host "   .\run_production.ps1 -SkipCleanup -SkipVerify" -ForegroundColor White
