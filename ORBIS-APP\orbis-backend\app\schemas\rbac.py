# app/schemas/rbac.py
"""
Schémas Pydantic pour le système RBAC
"""
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

class RoleBase(BaseModel):
    name: str = Field(..., max_length=50, description="Nom du rôle")
    description: Optional[str] = Field(None, description="Description du rôle")
    is_system_role: Optional[bool] = Field(False, description="Rôle système ou métier")

class RoleCreate(RoleBase):
    pass

class RoleUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=50)
    description: Optional[str] = None
    is_system_role: Optional[bool] = None

class Role(RoleBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True

class PermissionBase(BaseModel):
    name: str = Field(..., max_length=100, description="Nom de la permission (ex: projects.create)")
    resource: str = Field(..., max_length=50, description="<PERSON>ssour<PERSON> concernée (ex: projects)")
    action: str = Field(..., max_length=50, description="Action autorisée (ex: create)")
    description: Optional[str] = Field(None, description="Description de la permission")

class PermissionCreate(PermissionBase):
    pass

class PermissionUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100)
    resource: Optional[str] = Field(None, max_length=50)
    action: Optional[str] = Field(None, max_length=50)
    description: Optional[str] = None

class Permission(PermissionBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True

class WorkspaceRolePermissionBase(BaseModel):
    workspace_id: int
    role_name: str = Field(..., max_length=50, description="Nom du rôle")
    permission_id: int

class WorkspaceRolePermissionCreate(WorkspaceRolePermissionBase):
    pass

class WorkspaceRolePermission(WorkspaceRolePermissionBase):
    id: int
    created_at: datetime
    permission: Optional[Permission] = None

    class Config:
        from_attributes = True

# Alias de compatibilité
CompanyRolePermissionBase = WorkspaceRolePermissionBase
CompanyRolePermissionCreate = WorkspaceRolePermissionCreate
WorkspaceRolePermission = WorkspaceRolePermission

class RolePermissionsConfig(BaseModel):
    """Configuration des permissions pour un rôle dans un espace de travail"""
    workspace_id: int
    role_name: str
    permissions: List[str] = Field(..., description="Liste des noms de permissions")

class UserPermissions(BaseModel):
    """Permissions d'un utilisateur dans un espace de travail"""
    user_id: int
    workspace_id: int
    role_name: str
    permissions: List[str] = Field(default_factory=list, description="Liste des permissions")

class PermissionCheck(BaseModel):
    """Vérification de permission"""
    user_id: int
    workspace_id: int
    permission: str
    has_permission: bool

# Schémas pour les réponses API
class RoleWithPermissions(Role):
    """Rôle avec ses permissions dans un espace de travail"""
    permissions: List[Permission] = Field(default_factory=list)

class WorkspaceRolesSummary(BaseModel):
    """Résumé des rôles et permissions d'un espace de travail"""
    workspace_id: int
    workspace_name: str
    roles: List[RoleWithPermissions] = Field(default_factory=list)
    total_permissions: int = 0

# Alias de compatibilité
CompanyRolesSummary = WorkspaceRolesSummary

class UserRoleAssignment(BaseModel):
    """Attribution de rôle à un utilisateur"""
    user_id: int
    workspace_id: int
    role_name: str
    permissions: List[str] = Field(default_factory=list)

# Schémas pour la gestion des permissions par lot
class BulkPermissionUpdate(BaseModel):
    """Mise à jour en lot des permissions"""
    workspace_id: int
    role_name: str
    add_permissions: List[str] = Field(default_factory=list, description="Permissions à ajouter")
    remove_permissions: List[str] = Field(default_factory=list, description="Permissions à supprimer")

class PermissionMatrix(BaseModel):
    """Matrice des permissions pour un espace de travail"""
    workspace_id: int
    workspace_name: str
    roles: List[str] = Field(default_factory=list)
    permissions: List[str] = Field(default_factory=list)
    matrix: dict = Field(default_factory=dict, description="Matrice role -> permissions")
