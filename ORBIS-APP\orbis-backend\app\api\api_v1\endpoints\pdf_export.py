# app/api/api_v1/endpoints/pdf_export.py
"""
Endpoint pour l'export PDF avec WeasyPrint
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Optional, Dict, Any
import base64
import asyncio
import logging
import time
from concurrent.futures import ThreadPoolExecutor

from app.middleware.auth_sync_middleware import require_auth

# Import WeasyPrint (prioritaire)
try:
    # Configurer le PATH pour WeasyPrint sur Windows
    import os
    if os.name == 'nt':  # Windows
        msys2_path = r'C:\msys64\mingw64\bin'
        if msys2_path not in os.environ.get('PATH', ''):
            os.environ['PATH'] = os.environ.get('PATH', '') + ';' + msys2_path

    import weasyprint
    WEASYPRINT_AVAILABLE = True
except ImportError as e:
    WEASYPRINT_AVAILABLE = False

logger = logging.getLogger(__name__)

# Log de la disponibilité de WeasyPrint
if WEASYPRINT_AVAILABLE:
    logger.info("✅ WeasyPrint disponible")
else:
    logger.warning("⚠️ WeasyPrint non disponible")

router = APIRouter()


class PDFExportRequest(BaseModel):
    html: str
    filename: Optional[str] = "document.pdf"
    title: Optional[str] = "Document technique"
    format: Optional[str] = "A4"
    margin_top: Optional[str] = "25mm"
    margin_bottom: Optional[str] = "25mm"
    margin_left: Optional[str] = "20mm"
    margin_right: Optional[str] = "20mm"
    # Page de garde (optionnelle)
    cover_page_html: Optional[str] = None
    # En-têtes personnalisés
    header_left: Optional[str] = None
    header_center: Optional[str] = None
    header_right: Optional[str] = None
    # Pieds de page personnalisés
    footer_left: Optional[str] = None
    footer_center: Optional[str] = None
    footer_right: Optional[str] = None
    # Option pour activer/désactiver les en-têtes/pieds CSS
    enable_css_headers: Optional[bool] = False
    # Option pour désactiver les en-têtes/pieds sur la page de garde
    no_headers_on_cover: Optional[bool] = True
    # Option pour forcer le rechargement (éviter le cache)
    force_reload: Optional[bool] = False


class PDFExportResponse(BaseModel):
    pdf_base64: str
    filename: str
    size_bytes: int


@router.post("/export-pdf", response_model=PDFExportResponse)
async def export_pdf(
    request: PDFExportRequest,
    current_user: Dict[str, Any] = Depends(require_auth)
):
    """
    Exporte un document HTML en PDF en utilisant WeasyPrint
    """
    start_time = time.time()
    try:
        user_id = current_user.get("user_id")
        logger.info(f"🚀 Début export PDF pour utilisateur {user_id}")

        # 🔍 DEBUG: Analyser le contenu HTML reçu
        logger.info(f"🔍 HTML reçu - Longueur: {len(request.html)} caractères")
        logger.info(f"🔍 HTML début: {request.html[:200]}...")
        logger.info(f"🔍 HTML fin: ...{request.html[-200:]}")

        # Vérifier si une page de garde est présente
        has_cover_page = any(keyword in request.html.lower() for keyword in [
            'cover-page', 'page-garde', 'intervenants du lot', 'maîtrise d\'ouvrage'
        ])
        logger.info(f"🔍 Page de garde détectée: {has_cover_page}")

        if request.cover_page_html:
            logger.info(f"🔍 Page de garde séparée fournie: {len(request.cover_page_html)} caractères")
        
        # Utiliser WeasyPrint pour la génération PDF
        if WEASYPRINT_AVAILABLE:
            logger.info("📄 Utilisation de WeasyPrint pour la génération PDF")
            pdf_generation_start = time.time()

            pdf_buffer = await generate_pdf_with_weasyprint(
                request.html,
                format=request.format,
                margin_top=request.margin_top,
                margin_bottom=request.margin_bottom,
                margin_left=request.margin_left,
                margin_right=request.margin_right,
                cover_page_html=request.cover_page_html,
                header_left=request.header_left,
                header_center=request.header_center,
                header_right=request.header_right,
                footer_left=request.footer_left,
                footer_center=request.footer_center,
                footer_right=request.footer_right,
                enable_css_headers=request.enable_css_headers,
                no_headers_on_cover=request.no_headers_on_cover
            )

            pdf_generation_time = time.time() - pdf_generation_start
            logger.info(f"⏱️ Génération PDF terminée en {pdf_generation_time:.2f}s")
        else:
            raise HTTPException(status_code=500, detail="WeasyPrint non disponible")
        
        # Encoder en base64 pour la réponse
        encoding_start = time.time()
        pdf_base64 = base64.b64encode(pdf_buffer).decode('utf-8')
        encoding_time = time.time() - encoding_start

        total_time = time.time() - start_time

        logger.info(f"✅ Export PDF réussi - {len(pdf_buffer)} bytes, base64: {len(pdf_base64)} chars")
        logger.info(f"⏱️ Temps d'encodage base64: {encoding_time:.2f}s")
        logger.info(f"⏱️ Temps total d'export: {total_time:.2f}s")
        logger.info(f"📊 Vitesse: {len(pdf_buffer) / total_time / 1024:.1f} KB/s")

        return PDFExportResponse(
            pdf_base64=pdf_base64,
            filename=request.filename,
            size_bytes=len(pdf_buffer)
        )
        
    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"❌ Erreur lors de l'export PDF après {total_time:.2f}s: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de l'export PDF: {str(e)}")



async def generate_pdf_with_weasyprint(
    html: str,
    format: str = "A4",
    margin_top: str = "25mm",
    margin_bottom: str = "25mm",
    margin_left: str = "20mm",
    margin_right: str = "20mm",
    cover_page_html: str = None,
    header_left: str = None,
    header_center: str = None,
    header_right: str = None,
    footer_left: str = None,
    footer_center: str = None,
    footer_right: str = None,
    enable_css_headers: bool = False,
    no_headers_on_cover: bool = True,
    force_reload: bool = False
) -> bytes:
    """
    Génère un PDF à partir du HTML en utilisant WeasyPrint
    """
    try:
        def generate_pdf_sync():
            # Générer les en-têtes/pieds CSS si activés
            css_headers = ""
            # Utiliser des variables locales pour les marges ajustées
            final_margin_top = margin_top
            final_margin_bottom = margin_bottom

            if enable_css_headers:
                header_css = []
                footer_css = []

                # En-têtes
                if header_left:
                    header_css.append(f'@top-left {{ content: "{header_left}"; font-size: 10pt; color: #333; }}')
                if header_center:
                    header_css.append(f'@top-center {{ content: "{header_center}"; font-size: 10pt; color: #333; text-align: center; }}')
                if header_right:
                    header_css.append(f'@top-right {{ content: "{header_right}"; font-size: 10pt; color: #333; }}')

                # Pieds de page
                if footer_left:
                    footer_css.append(f'@bottom-left {{ content: "{footer_left}"; font-size: 9pt; color: #666; }}')
                if footer_center:
                    footer_css.append(f'@bottom-center {{ content: "{footer_center}"; font-size: 9pt; color: #666; text-align: center; }}')
                if footer_right:
                    footer_css.append(f'@bottom-right {{ content: "{footer_right}"; font-size: 9pt; color: #666; }}')

                css_headers = "\n                ".join(header_css + footer_css)

                # Ajuster les marges si on a des en-têtes/pieds
                if header_css:
                    final_margin_top = "40mm"
                if footer_css:
                    final_margin_bottom = "35mm"

            # CSS complet avec support WeasyPrint - Pages nommées
            css_styles = f"""
            /* Page de garde sans marges ni headers/footers */
            @page cover {{
                size: {format};
                margin: 0mm;
                @top-center {{ content: none; }}
                @bottom-center {{ content: none; }}
                @top-left {{ content: none; }}
                @top-right {{ content: none; }}
                @bottom-left {{ content: none; }}
                @bottom-right {{ content: none; }}
            }}

            /* Pages normales avec marges et headers/footers */
            @page normal {{
                size: {format};
                margin: {final_margin_top} {margin_right} {final_margin_bottom} {margin_left};
                {css_headers}
                @top-center {{ content: "CCTP"; }}
                @bottom-center {{ content: none; }}
                @top-left {{ content: none; }}
                @top-right {{ content: none; }}
                @bottom-left {{ content: none; }}
                @bottom-right {{ content: none; }}
            }}

            body {{
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                font-size: 12pt;
                line-height: 1.5;
                color: #333;
                margin: 0;
                padding: 0;
            }}

            .project-name {{
                font-size: 35pt;
                font-weight: 800;
                color: #222;
                margin: 0 0 8pt 0;
                text-align: center;
            }}

            .project-address {{
                font-size: 15pt;
                color: #333;
                line-height: 1.4;
                text-align: center;
                margin-bottom: 20pt;
            }}

            .cctp-label {{
                font-size: 25pt;
                font-weight: bold;
                text-align: center;
                color: #0f766e;
                margin: 20pt 0;
                border: 3px solid #0f766e;
                padding: 10pt;
                border-radius: 8pt;
            }}

            h1, h2, h3, h4, h5, h6 {{
                color: #0f766e;
                margin-top: 20pt;
                margin-bottom: 12pt;
                font-weight: 600;
                border-bottom: 2px solid #0f766e;
                padding-bottom: 4pt;
            }}

            h1 {{
                font-size: 20pt;
                font-weight: 700;
                border-bottom: 3px solid #0f766e;
            }}
            h2 {{
                font-size: 16pt;
                border-bottom: 2px solid #0f766e;
            }}
            h3 {{
                font-size: 14pt;
                border-bottom: 1px solid #0f766e;
            }}

            table {{
                border-collapse: collapse;
                width: 100%;
                margin: 16pt 0;
                border: 2px solid #0f766e;
            }}

            th {{
                background-color: #0f766e;
                color: white;
                font-weight: bold;
                text-align: center;
                border: 1px solid #0f766e;
                padding: 8pt;
            }}

            td {{
                border: 1px solid #d1d5db;
                padding: 8pt;
                text-align: left;
            }}

            p {{
                margin-bottom: 12pt;
                text-align: justify;
            }}

            ul, ol {{
                margin: 12pt 0;
                padding-left: 20pt;
            }}

            li {{
                margin-bottom: 6pt;
            }}

            a, .technical-link {{
                color: #0f766e;
                text-decoration: underline;
            }}

            /* Classes pour assigner les pages nommées */
            .cover-page {{
                page: cover;
                page-break-after: always;
            }}

            .content-page {{
                page: normal;
                page-break-before: always;
            }}





            /* CSS pour la page de garde supprimé - utilise les styles inline du frontend */
            """

            # Construire le HTML complet avec page de garde optionnelle
            body_content = ""
            logger.info("cover_page_html:", cover_page_html);
            # 1. Page de garde en premier (si fournie)
            if cover_page_html:
                # Utiliser directement le HTML de la page de garde sans ajouter de wrapper
                # Le HTML du frontend contient déjà tous les styles nécessaires
                body_content += f"""
                <div style="page-break-after: always;">
                    {cover_page_html}
                </div>
                """

            # 2. Contenu principal avec classe pour page normale
            if cover_page_html:
                # Si on a une page de garde, le contenu va sur une page normale
                body_content += f'<div class="content-page">{html}</div>'
            else:
                # Pas de page de garde, le contenu va directement sur page normale
                body_content += f'<div class="content-page" style="page-break-before: auto;">{html}</div>'

            # HTML complet avec timestamp pour éviter le cache
            import time
            cache_buster = int(time.time() * 1000) if force_reload else ""

            full_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="cache-control" content="no-cache, no-store, must-revalidate">
                <meta name="pragma" content="no-cache">
                <meta name="expires" content="0">
                <!-- Cache buster: {cache_buster} -->
                <style>{css_styles}</style>
            </head>
            <body>
                {body_content}
            </body>
            </html>
            """

            # 🔍 DEBUG: Sauvegarder le HTML complet envoyé à WeasyPrint
            debug_filename = f"weasyprint_debug_{format}_{margin_top}_{margin_bottom}.html"
            try:
                with open(debug_filename, "w", encoding="utf-8") as debug_file:
                    debug_file.write(full_html)
                logger.info(f"🔍 HTML debug sauvegardé: {debug_filename}")
            except Exception as e:
                logger.warning(f"⚠️ Impossible de sauvegarder le debug HTML: {e}")

            # 🔍 DEBUG: Sauvegarder aussi juste les styles CSS
            css_debug_filename = f"weasyprint_styles_debug.css"
            try:
                with open(css_debug_filename, "w", encoding="utf-8") as css_file:
                    css_file.write(css_styles)
                logger.info(f"🔍 CSS debug sauvegardé: {css_debug_filename}")
            except Exception as e:
                logger.warning(f"⚠️ Impossible de sauvegarder le debug CSS: {e}")

            # Générer le PDF avec WeasyPrint
            weasyprint_start = time.time()

            # Plus besoin de CSS externe, tout est dans le CSS principal
            pdf_bytes = weasyprint.HTML(string=full_html).write_pdf()
            weasyprint_time = time.time() - weasyprint_start

            # 🔍 DEBUG: Log des informations de génération
            logger.info(f"🔍 WeasyPrint - HTML length: {len(full_html)} chars")
            logger.info(f"🔍 WeasyPrint - CSS length: {len(css_styles)} chars")
            logger.info(f"🔍 WeasyPrint - PDF size: {len(pdf_bytes)} bytes")
            logger.info(f"⏱️ WeasyPrint génération: {weasyprint_time:.2f}s")

            return pdf_bytes

        # Exécuter dans un thread séparé
        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor() as executor:
            pdf_buffer = await loop.run_in_executor(executor, generate_pdf_sync)

        logger.info(f"PDF généré avec WeasyPrint: {len(pdf_buffer)} bytes")
        return pdf_buffer

    except Exception as e:
        logger.error(f"Erreur WeasyPrint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erreur WeasyPrint: {str(e)}")
