
            /* Reset complet */
            * {
                box-sizing: border-box;
            }

            /* Page de garde sans marges ni headers/footers */
            @page cover {
                size: A4;
                margin: 0mm;
                @top-center { content: none; }
                @bottom-center { content: none; }
                @top-left { content: none; }
                @top-right { content: none; }
                @bottom-left { content: none; }
                @bottom-right { content: none; }
            }

            /* Pages normales avec marges et headers/footers */
            @page normal {
                size: A4;
                margin: 25mm 20mm 25mm 20mm;
                
                @top-center { content: "CCTP"; }
                @bottom-center { content: none; }
                @top-left { content: none; }
                @top-right { content: none; }
                @bottom-left { content: none; }
                @bottom-right { content: none; }
            }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                font-size: 12pt;
                line-height: 1.5;
                color: #333;
                margin: 0;
                padding: 0;
            }

            .project-name {
                font-size: 35pt;
                font-weight: 800;
                color: #222;
                margin: 0 0 8pt 0;
                text-align: center;
            }

            .project-address {
                font-size: 15pt;
                color: #333;
                line-height: 1.4;
                text-align: center;
                margin-bottom: 20pt;
            }

            .cctp-label {
                font-size: 25pt;
                font-weight: bold;
                text-align: center;
                color: #0f766e;
                margin: 20pt 0;
                border: 3px solid #0f766e;
                padding: 10pt;
                border-radius: 8pt;
            }

            h1, h2, h3, h4, h5, h6 {
                color: #0f766e;
                margin-top: 20pt;
                margin-bottom: 12pt;
                font-weight: 600;
                border-bottom: 2px solid #0f766e;
                padding-bottom: 4pt;
            }

            h1 {
                font-size: 20pt;
                font-weight: 700;
                border-bottom: 3px solid #0f766e;
            }
            h2 {
                font-size: 16pt;
                border-bottom: 2px solid #0f766e;
            }
            h3 {
                font-size: 14pt;
                border-bottom: 1px solid #0f766e;
            }

            table {
                border-collapse: collapse;
                width: 100%;
                margin: 16pt 0;
                border: 2px solid #0f766e;
            }

            th {
                background-color: #0f766e;
                color: white;
                font-weight: bold;
                text-align: center;
                border: 1px solid #0f766e;
                padding: 8pt;
            }

            td {
                border: 1px solid #d1d5db;
                padding: 8pt;
                text-align: left;
            }

            p {
                margin-bottom: 12pt;
                text-align: justify;
            }

            ul, ol {
                margin: 12pt 0;
                padding-left: 20pt;
            }

            li {
                margin-bottom: 6pt;
            }

            a, .technical-link {
                color: #0f766e;
                text-decoration: underline;
            }

            /* Classes pour assigner les pages nommées */
            .cover-page {
                page: cover;
                width: 210mm; /* Largeur A4 */
                height: 297mm; /* Hauteur A4 */
                margin: 0;
                padding: 0;
                box-sizing: border-box;
                overflow: hidden;
                page-break-after: always;
            }

            .cover-page-static {
                page: cover;
                width: 210mm;
                height: 297mm;
                margin: 0;
                padding: 0;
                display: flex;
                box-sizing: border-box;
                overflow: hidden;
            }

            .content-page {
                page: normal;
                page-break-before: always;
            }

            .cover-page-static .left-column {
                width: 50%;
                background-color: #0f766e;
                padding: 15px;
                color: white;
                display: flex;
                flex-direction: column;
                box-sizing: border-box;
                overflow: hidden;
            }

            /* Photo du projet dans la colonne gauche */
            .cover-page-static .project-photo-img {
                width: 100%;
                max-height: 400px;
                object-fit: cover;
                border-radius: 4px;
                margin-bottom: 15px;
            }

            .cover-page-static .right-column {
                width: 50%;
                background-color: white;
                padding: 15px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                box-sizing: border-box;
                overflow: hidden;
            }

            .cover-page-static .intervenants-table {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 10px;
                overflow: hidden;
            }

            .cover-page-static .intervenant-row {
                display: flex;
                gap: 8px;
                padding: 8px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 4px;
                align-items: center;
                min-height: 60px;
                box-sizing: border-box;
            }

            .cover-page-static .logo-cell {
                flex-shrink: 0;
                width: 60px;
                height: 60px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .cover-page-static .company-logo {
                width: 60px !important;
                height: 60px !important;
                max-width: 60px !important;
                max-height: 60px !important;
                object-fit: contain !important;
                display: block;
            }

            .cover-page-static .info-cell {
                flex: 1;
                min-width: 0;
                overflow: hidden;
            }

            .cover-page-static .company-name {
                font-size: 15px;
                font-weight: 600;
                margin: 0 0 2px 0;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .cover-page-static .company-details {
                font-size: 10px;
                opacity: 0.9;
                line-height: 1.2;
                word-wrap: break-word;
            }

            .cover-page-static .project-photo {
                width: 100%;
                max-height: 200px;
                object-fit: cover;
                border-radius: 4px;
                margin-bottom: 15px;
            }

            .cover-page-static .project-name {
                font-size: 18px;
                font-weight: 700;
                color: #0f766e;
                margin: 0 0 8px 0;
                text-align: center;
                word-wrap: break-word;
            }

            .cover-page-static .project-address {
                font-size: 12px;
                color: #666;
                text-align: center;
                margin-bottom: 15px;
                word-wrap: break-word;
            }

            /* Logo du workspace */
            .cover-page-static .workspace-logo {
                text-align: center;
                margin-bottom: 20px;
                width: 100%;
                height: 80px;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .cover-page-static .workspace-logo img,
            .cover-page-static .workspace-logo-img {
                width: auto !important;
                height: 80px !important;
                max-width: 200px !important;
                max-height: 80px !important;
                object-fit: contain !important;
                display: block;
            }

            /* Logos MOA */
            .cover-page-static .moa-logo {
                width: 100%;
                max-height: 300px;
                display: flex;
                align-items: center;
                justify-content: center;
                overflow: hidden;
            }

            .cover-page-static .moa-logo img,
            .cover-page-static .moa-logo-img {
                width: 100% !important;
                max-height: 50px !important;
                display: block;
            }

            .cover-page-static .cctp-label {
                font-size: 20px;
                font-weight: 700;
                text-align: center;
                color: #0f766e;
                margin: 15px 0;
                padding: 10px;
                border: 2px solid #0f766e;
                border-radius: 4px;
            }

            .cover-page-static .lot-info {
                text-align: center;
                font-size: 14px;
                color: #333;
                margin-bottom: 15px;
            }

            .cover-page-static table {
                width: 100%;
                border-collapse: collapse;
                font-size: 10px;
                margin-top: 10px;
            }

            .cover-page-static table td {
                border: 1px solid #0f766e;
                padding: 4px 6px;
                text-align: left;
                word-wrap: break-word;
                max-width: 0;
            }

            .cover-page-static table td:first-child {
                background: #f0fdfa;
                font-weight: 600;
                width: 30%;
            }
            