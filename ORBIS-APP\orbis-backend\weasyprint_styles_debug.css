
            @page cover {
                size: A4;
                margin: 0mm;
            }

            @page normal {
                size: A4;
                margin: 25mm 20mm 25mm 20mm;
                
            }

            .cover-page {
                page: cover;
                page-break-after: always;
            }

            .content-page {
                page: normal;
                page-break-before: always;
            }

            .cover-page {
                page: cover;
                page-break-after: always;
            }

            .content-page {
                page: normal;
                page-break-before: always;
            }





            /* CSS pour la page de garde supprimé - utilise les styles inline du frontend */
            