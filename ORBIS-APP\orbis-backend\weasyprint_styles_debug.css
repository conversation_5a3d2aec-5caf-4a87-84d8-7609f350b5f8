
            /* Reset complet */
            * {
                box-sizing: border-box;
            }

            /* Page de garde sans marges ni headers/footers */
            @page cover {
                size: A4;
                margin: 0mm;
                @top-center { content: none; }
                @bottom-center { content: none; }
                @top-left { content: none; }
                @top-right { content: none; }
                @bottom-left { content: none; }
                @bottom-right { content: none; }
            }

            /* Pages normales avec marges et headers/footers */
            @page normal {
                size: A4;
                margin: 25mm 20mm 25mm 20mm;
                
                @top-center { content: "CCTP"; }
                @bottom-center { content: none; }
                @top-left { content: none; }
                @top-right { content: none; }
                @bottom-left { content: none; }
                @bottom-right { content: none; }
            }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                font-size: 12pt;
                line-height: 1.5;
                color: #333;
                margin: 0;
                padding: 0;
            }

            .project-name {
                font-size: 35pt;
                font-weight: 800;
                color: #222;
                margin: 0 0 8pt 0;
                text-align: center;
            }

            .project-address {
                font-size: 15pt;
                color: #333;
                line-height: 1.4;
                text-align: center;
                margin-bottom: 20pt;
            }

            .cctp-label {
                font-size: 25pt;
                font-weight: bold;
                text-align: center;
                color: #0f766e;
                margin: 20pt 0;
                border: 3px solid #0f766e;
                padding: 10pt;
                border-radius: 8pt;
            }

            h1, h2, h3, h4, h5, h6 {
                color: #0f766e;
                margin-top: 20pt;
                margin-bottom: 12pt;
                font-weight: 600;
                border-bottom: 2px solid #0f766e;
                padding-bottom: 4pt;
            }

            h1 {
                font-size: 20pt;
                font-weight: 700;
                border-bottom: 3px solid #0f766e;
            }
            h2 {
                font-size: 16pt;
                border-bottom: 2px solid #0f766e;
            }
            h3 {
                font-size: 14pt;
                border-bottom: 1px solid #0f766e;
            }

            table {
                border-collapse: collapse;
                width: 100%;
                margin: 16pt 0;
                border: 2px solid #0f766e;
            }

            th {
                background-color: #0f766e;
                color: white;
                font-weight: bold;
                text-align: center;
                border: 1px solid #0f766e;
                padding: 8pt;
            }

            td {
                border: 1px solid #d1d5db;
                padding: 8pt;
                text-align: left;
            }

            p {
                margin-bottom: 12pt;
                text-align: justify;
            }

            ul, ol {
                margin: 12pt 0;
                padding-left: 20pt;
            }

            li {
                margin-bottom: 6pt;
            }

            a, .technical-link {
                color: #0f766e;
                text-decoration: underline;
            }

            /* Classes pour assigner les pages nommées */
            .cover-page {
                page: cover;
                page-break-after: always;
            }

            .content-page {
                page: normal;
                page-break-before: always;
            }





            /* CSS pour la page de garde supprimé - utilise les styles inline du frontend */
            