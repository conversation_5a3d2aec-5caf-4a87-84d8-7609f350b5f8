# app/api/api_v1/endpoints/admin_workspaces.py
from typing import Any, List, Dict
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from pydantic import BaseModel

from app.core.database import get_db
from app.middleware.auth_sync_middleware import require_auth, require_admin, require_superuser
from app.models.user import User
from app.models.workspace import Workspace, UserWorkspace, WorkspaceSettings
from app.schemas.workspace import Workspace as WorkspaceSchema, WorkspaceCreate, WorkspaceUpdate

router = APIRouter()

# Pydantic models pour l'admin
class AdminWorkspaceResponse(BaseModel):
    id: int
    name: str
    code: str
    description: str = None
    address: str = None
    phone: str = None
    email: str = None
    website: str = None
    is_active: bool
    created_at: str
    updated_at: str
    user_count: int = 0

    class Config:
        from_attributes = True

@router.get("/", response_model=List[AdminWorkspaceResponse])
async def list_all_workspaces(
    db: AsyncSession = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: Dict[str, Any] = Depends(require_superuser)
):
    """
    Lister tous les espaces de travail (super admin seulement)
    """
    try:
        print(f"🔍 AdminWorkspaces - Récupération des espaces de travail...")

        # Récupérer tous les espaces de travail avec le nombre d'utilisateurs
        result = await db.execute(
            select(
                Workspace,
                func.count(UserWorkspace.id).label('user_count')
            )
            .outerjoin(UserWorkspace, Workspace.id == UserWorkspace.workspace_id)
            .group_by(Workspace.id)
            .offset(skip)
            .limit(limit)
        )
        
        workspaces_data = result.all()
        
        workspaces = []
        for workspace, user_count in workspaces_data:
            workspace_dict = {
                "id": workspace.id,
                "name": workspace.name,
                "code": workspace.code,
                "description": workspace.description,
                "address": workspace.address,
                "phone": workspace.phone,
                "email": workspace.email,
                "website": workspace.website,
                "is_active": workspace.is_active,
                "created_at": workspace.created_at.isoformat() if workspace.created_at else None,
                "updated_at": workspace.updated_at.isoformat() if workspace.updated_at else None,
                "user_count": user_count or 0
            }
            workspaces.append(AdminWorkspaceResponse(**workspace_dict))

        print(f"✅ AdminWorkspaces - {len(workspaces)} espaces de travail trouvés")
        return workspaces

    except Exception as e:
        print(f"❌ AdminWorkspaces - Erreur: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la récupération des espaces de travail: {str(e)}"
        )

@router.get("/{workspace_id}", response_model=AdminWorkspaceResponse)
async def get_workspace_by_id(
    workspace_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_superuser)
):
    """
    Récupérer un espace de travail par ID (super admin seulement)
    """
    try:
        print(f"🔍 AdminWorkspaces - Récupération de l'espace de travail {workspace_id}...")

        # Récupérer l'espace de travail avec le nombre d'utilisateurs
        result = await db.execute(
            select(
                Workspace,
                func.count(UserWorkspace.id).label('user_count')
            )
            .outerjoin(UserWorkspace, Workspace.id == UserWorkspace.workspace_id)
            .where(Workspace.id == workspace_id)
            .group_by(Workspace.id)
        )
        
        workspace_data = result.first()
        
        if not workspace_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Espace de travail non trouvé"
            )

        workspace, user_count = workspace_data
        
        workspace_dict = {
            "id": workspace.id,
            "name": workspace.name,
            "code": workspace.code,
            "description": workspace.description,
            "address": workspace.address,
            "phone": workspace.phone,
            "email": workspace.email,
            "website": workspace.website,
            "is_active": workspace.is_active,
            "created_at": workspace.created_at.isoformat() if workspace.created_at else None,
            "updated_at": workspace.updated_at.isoformat() if workspace.updated_at else None,
            "user_count": user_count or 0
        }

        print(f"✅ AdminWorkspaces - Espace de travail {workspace_id} trouvé")
        return AdminWorkspaceResponse(**workspace_dict)

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ AdminWorkspaces - Erreur: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la récupération de l'espace de travail: {str(e)}"
        )

@router.post("/", response_model=AdminWorkspaceResponse)
async def create_workspace_admin(
    workspace_in: WorkspaceCreate,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_superuser)
):
    """
    Créer un nouvel espace de travail (super admin seulement)
    """
    try:
        print(f"🔍 AdminWorkspaces - Création d'un espace de travail: {workspace_in.name}")

        # Vérifier si le code existe déjà
        result = await db.execute(
            select(Workspace).where(Workspace.code == workspace_in.code)
        )
        existing_workspace = result.scalar_one_or_none()
        
        if existing_workspace:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Le code de l'espace de travail existe déjà"
            )

        # Créer l'espace de travail
        workspace = Workspace(**workspace_in.dict())
        db.add(workspace)
        await db.flush()  # Pour obtenir l'ID
        
        # Créer les paramètres par défaut
        workspace_settings = WorkspaceSettings(
            workspace_id=workspace.id,
            default_currency="EUR",
            language="fr"
        )
        db.add(workspace_settings)
        
        await db.commit()
        await db.refresh(workspace)

        workspace_dict = {
            "id": workspace.id,
            "name": workspace.name,
            "code": workspace.code,
            "description": workspace.description,
            "address": workspace.address,
            "phone": workspace.phone,
            "email": workspace.email,
            "website": workspace.website,
            "is_active": workspace.is_active,
            "created_at": workspace.created_at.isoformat() if workspace.created_at else None,
            "updated_at": workspace.updated_at.isoformat() if workspace.updated_at else None,
            "user_count": 0
        }

        print(f"✅ AdminWorkspaces - Espace de travail créé avec l'ID {workspace.id}")
        return AdminWorkspaceResponse(**workspace_dict)

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ AdminWorkspaces - Erreur: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la création de l'espace de travail: {str(e)}"
        )

@router.put("/{workspace_id}", response_model=AdminWorkspaceResponse)
async def update_workspace_admin(
    workspace_id: int,
    workspace_in: WorkspaceUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_superuser)
):
    """
    Mettre à jour un espace de travail (super admin seulement)
    """
    try:
        print(f"🔍 AdminWorkspaces - Mise à jour de l'espace de travail {workspace_id}")

        # Récupérer l'espace de travail
        result = await db.execute(
            select(Workspace).where(Workspace.id == workspace_id)
        )
        workspace = result.scalar_one_or_none()
        
        if not workspace:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Espace de travail non trouvé"
            )

        # Vérifier si le nouveau code existe déjà (si fourni)
        if workspace_in.code and workspace_in.code != workspace.code:
            result = await db.execute(
                select(Workspace).where(Workspace.code == workspace_in.code)
            )
            existing_workspace = result.scalar_one_or_none()
            
            if existing_workspace:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Le code de l'espace de travail existe déjà"
                )

        # Mettre à jour les champs
        update_data = workspace_in.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(workspace, field, value)

        await db.commit()
        await db.refresh(workspace)

        # Récupérer le nombre d'utilisateurs
        result = await db.execute(
            select(func.count(UserWorkspace.id))
            .where(UserWorkspace.workspace_id == workspace_id)
        )
        user_count = result.scalar() or 0

        workspace_dict = {
            "id": workspace.id,
            "name": workspace.name,
            "code": workspace.code,
            "description": workspace.description,
            "address": workspace.address,
            "phone": workspace.phone,
            "email": workspace.email,
            "website": workspace.website,
            "is_active": workspace.is_active,
            "created_at": workspace.created_at.isoformat() if workspace.created_at else None,
            "updated_at": workspace.updated_at.isoformat() if workspace.updated_at else None,
            "user_count": user_count
        }

        print(f"✅ AdminWorkspaces - Espace de travail {workspace_id} mis à jour")
        return AdminWorkspaceResponse(**workspace_dict)

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ AdminWorkspaces - Erreur: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la mise à jour de l'espace de travail: {str(e)}"
        )

@router.delete("/{workspace_id}")
async def delete_workspace_admin(
    workspace_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_superuser)
):
    """
    Supprimer un espace de travail (super admin seulement)
    """
    try:
        print(f"🔍 AdminWorkspaces - Suppression de l'espace de travail {workspace_id}")

        # Récupérer l'espace de travail
        result = await db.execute(
            select(Workspace).where(Workspace.id == workspace_id)
        )
        workspace = result.scalar_one_or_none()
        
        if not workspace:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Espace de travail non trouvé"
            )

        # Suppression logique (soft delete)
        workspace.is_active = False
        await db.commit()

        print(f"✅ AdminWorkspaces - Espace de travail {workspace_id} supprimé")
        return {"message": "Espace de travail supprimé avec succès"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ AdminWorkspaces - Erreur: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la suppression de l'espace de travail: {str(e)}"
        )
