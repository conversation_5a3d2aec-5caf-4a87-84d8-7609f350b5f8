# Dockerfile optimisé pour Render.com
FROM python:3.11-slim

# Définir le répertoire de travail
WORKDIR /app

# Installer les dépendances système nécessaires
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Copier les fichiers de dépendances
COPY requirements.txt .

# Installer les dépendances Python
RUN pip install --no-cache-dir -r requirements.txt

# Copier le code source
COPY . .

# Créer le dossier uploads avec les bonnes permissions
RUN mkdir -p uploads/project_photos && chmod -R 755 uploads

# Exposer le port
EXPOSE 8000

# Commande de démarrage optimisée pour Render
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
