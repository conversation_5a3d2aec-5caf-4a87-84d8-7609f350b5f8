@echo off
echo ================================================
echo    🚀 ORBIS Backend - Déploiement Render.com
echo ================================================
echo.

echo 📋 Vérification de la configuration...
echo.

REM Vérifier les fichiers nécessaires
if exist render.yaml (
    echo ✅ render.yaml trouve
) else (
    echo ❌ render.yaml manquant
    pause
    exit /b 1
)

if exist Dockerfile.render (
    echo ✅ Dockerfile.render trouve
) else (
    echo ❌ Dockerfile.render manquant
    pause
    exit /b 1
)

echo.
echo 🧹 Nettoyage du projet...
echo.

REM Nettoyer les fichiers de test
if exist test_lots_results.json del test_lots_results.json
if exist test_user_credentials.json del test_user_credentials.json
if exist auth_token.txt del auth_token.txt
if exist weasyprint_styles_debug.css del weasyprint_styles_debug.css
if exist template_config.json del template_config.json

echo ✅ Nettoyage termine

echo.
echo 📝 Instructions de deploiement :
echo.
echo 1. Poussez votre code sur GitHub
echo 2. Allez sur https://render.com
echo 3. Cliquez sur "New" -^> "Web Service"
echo 4. Connectez votre repository GitHub
echo 5. Selectionnez le repository orbis-backend
echo.
echo 🎯 Configuration rapide :
echo    Build Command: pip install -r requirements.txt
echo    Start Command: uvicorn app.main:app --host 0.0.0.0 --port $PORT
echo    Environment: Python 3.11
echo.
echo 🌐 Deploy to Render button :
echo    https://render.com/deploy?repo=https://github.com/YOUR_USERNAME/orbis-backend
echo.
echo ================================================
echo    ✅ Projet pret pour le deploiement !
echo ================================================
pause
